using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Simple test script to validate the simplified projectile system
    /// Can be attached to any GameObject to test projectile creation and functionality
    /// </summary>
    public class SimplifiedProjectileTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private bool createTestPrefab = true;
        [SerializeField] private Transform spawnPoint;
        [SerializeField] private Transform testTarget;
        
        [Header("Projectile Settings")]
        [SerializeField] private float testDamage = 25f;
        [SerializeField] private float testSpeed = 50f;
        [SerializeField] private float testLifetime = 5f;
        [SerializeField] private bool testHoming = true;
        [SerializeField] private float testScale = 1f;
        
        private GameObject testProjectilePrefab;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                RunSimplifiedProjectileTest();
            }
        }
        
        [ContextMenu("Run Simplified Projectile Test")]
        public void RunSimplifiedProjectileTest()
        {
            Debug.Log("=== SIMPLIFIED PROJECTILE SYSTEM TEST ===");
            
            // Test 1: Create test prefab
            if (createTestPrefab)
            {
                TestCreateProjectilePrefab();
            }
            
            // Test 2: Test projectile functionality
            if (testProjectilePrefab != null)
            {
                TestProjectileFunctionality();
            }
            
            Debug.Log("=== SIMPLIFIED PROJECTILE TEST COMPLETED ===");
        }
        
        private void TestCreateProjectilePrefab()
        {
            Debug.Log("--- Test 1: Create Test Projectile Prefab ---");
            
            // Create root GameObject
            GameObject projectileGO = new GameObject("TestSimplifiedProjectile");
            
            // Add Unity components
            Rigidbody rb = projectileGO.AddComponent<Rigidbody>();
            rb.useGravity = false;
            rb.isKinematic = false; // Allow movement
            rb.interpolation = RigidbodyInterpolation.Interpolate;
            
            SphereCollider collider = projectileGO.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            collider.radius = 0.5f;
            
            // Add Chronos Timeline if available
            var timelineType = System.Type.GetType("Chronos.Timeline, Assembly-CSharp");
            if (timelineType != null)
            {
                projectileGO.AddComponent(timelineType);
                Debug.Log("Added Chronos Timeline component");
            }
            
            // Add simplified component system
            ProjectileEntity projectileEntity = projectileGO.AddComponent<ProjectileEntity>();
            ProjectileCore coreComponent = projectileGO.AddComponent<ProjectileCore>();
            ProjectileInteraction interactionComponent = projectileGO.AddComponent<ProjectileInteraction>();
            
            // Set component references
            projectileEntity.coreComponent = coreComponent;
            projectileEntity.interactionComponent = interactionComponent;
            
            // Add test component
            projectileGO.AddComponent<ProjectileEntityTest>();
            
            // Create visual model
            GameObject modelChild = new GameObject("Projectile Model");
            modelChild.transform.SetParent(projectileGO.transform);
            modelChild.transform.localPosition = Vector3.zero;
            
            MeshRenderer renderer = modelChild.AddComponent<MeshRenderer>();
            MeshFilter meshFilter = modelChild.AddComponent<MeshFilter>();
            
            // Create sphere mesh
            GameObject tempSphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            meshFilter.mesh = tempSphere.GetComponent<MeshFilter>().sharedMesh;
            DestroyImmediate(tempSphere);
            
            // Create simple material
            Material testMaterial = new Material(Shader.Find("Standard"));
            testMaterial.color = Color.cyan;
            testMaterial.name = "TestProjectileMaterial";
            renderer.material = testMaterial;
            
            // Position at spawn point
            if (spawnPoint != null)
            {
                projectileGO.transform.position = spawnPoint.position;
                projectileGO.transform.rotation = spawnPoint.rotation;
            }
            
            testProjectilePrefab = projectileGO;
            
            Debug.Log("✅ Test projectile prefab created successfully");
        }
        
        private void TestProjectileFunctionality()
        {
            Debug.Log("--- Test 2: Test Projectile Functionality ---");
            
            if (testProjectilePrefab == null)
            {
                Debug.LogError("❌ Test projectile prefab is null");
                return;
            }
            
            ProjectileEntity projectileEntity = testProjectilePrefab.GetComponent<ProjectileEntity>();
            if (projectileEntity == null)
            {
                Debug.LogError("❌ ProjectileEntity component not found");
                return;
            }
            
            // Test setup projectile
            projectileEntity.IsPlayerShot = true; // Set as player shot for testing
            projectileEntity.SetupProjectile(testDamage, testSpeed, testLifetime, testHoming, testScale, testTarget);
            
            // Verify properties
            Debug.Log($"Bullet Speed: {projectileEntity.BulletSpeed} (expected: {testSpeed})");
            Debug.Log($"Damage Amount: {projectileEntity.DamageAmount} (expected: {testDamage})");
            Debug.Log($"Lifetime: {projectileEntity.Lifetime} (expected: {testLifetime})");
            Debug.Log($"Is Player Shot: {projectileEntity.IsPlayerShot}");
            
            // Test component references
            bool coreOk = projectileEntity.coreComponent != null;
            bool interactionOk = projectileEntity.interactionComponent != null;
            
            Debug.Log($"Core Component: {(coreOk ? "✅" : "❌")}");
            Debug.Log($"Interaction Component: {(interactionOk ? "✅" : "❌")}");
            
            if (coreOk)
            {
                Debug.Log($"Core Speed: {projectileEntity.coreComponent.bulletSpeed}");
                Debug.Log($"Core Homing: {projectileEntity.coreComponent.homing}");
                Debug.Log($"Core Target: {(projectileEntity.coreComponent.currentTarget != null ? projectileEntity.coreComponent.currentTarget.name : "None")}");
            }
            
            if (interactionOk)
            {
                Debug.Log($"Interaction Damage: {projectileEntity.interactionComponent.damageAmount}");
            }
            
            // Test homing toggle
            projectileEntity.EnableHoming(false);
            projectileEntity.EnableHoming(true);
            
            Debug.Log("✅ Projectile functionality test complete");
        }
        
        [ContextMenu("Cleanup Test Projectile")]
        public void CleanupTestProjectile()
        {
            if (testProjectilePrefab != null)
            {
                DestroyImmediate(testProjectilePrefab);
                testProjectilePrefab = null;
                Debug.Log("Test projectile cleaned up");
            }
        }
        
        private void OnDrawGizmos()
        {
            // Draw spawn point
            if (spawnPoint != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(spawnPoint.position, 0.5f);
                Gizmos.DrawRay(spawnPoint.position, spawnPoint.forward * 2f);
            }
            
            // Draw test target
            if (testTarget != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(testTarget.position, Vector3.one);
            }
        }
    }
}

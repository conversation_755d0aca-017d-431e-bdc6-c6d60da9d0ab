using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using System.Reflection;

namespace BTR
{
    /// <summary>
    /// Specialized analyzer for async loading operations and cancellation token behavior.
    /// Focuses on identifying specific failure patterns in UniTask-based scene loading.
    /// </summary>
    public class AsyncLoadingAnalyzer : MonoBehaviour
    {
        [Header("Analysis Settings")]
        [SerializeField] private bool enableAnalysis = true;
        [SerializeField] private bool trackCancellationTokens = true;
        [SerializeField] private bool monitorUniTaskOperations = true;
        [SerializeField] private bool analyzeRetryPatterns = true;
        [SerializeField] private float analysisInterval = 1f;

        [Header("Detection Thresholds")]
        [SerializeField] private float suspiciousLoadTime = 10f;
        [SerializeField] private int maxRetryAttempts = 3;
        [SerializeField] private float tokenTimeoutThreshold = 30f;

        // Analysis Data
        private readonly Dictionary<string, AsyncLoadingOperation> activeOperations = new();
        private readonly List<AsyncLoadingFailure> recentFailures = new();
        private readonly Dictionary<string, int> retryCounters = new();
        private readonly HashSet<CancellationToken> trackedTokens = new();

        // Pattern Detection
        private int consecutiveFailures;
        private float lastFailureTime;
        private string mostProblematicScene;
        private int maxFailuresForScene;

        // Performance Metrics
        private float totalAnalysisTime;
        private int totalOperationsAnalyzed;
        private int cancelledOperations;
        private int timeoutOperations;
        private int successfulOperations;

        public struct AsyncLoadingOperation
        {
            public string SceneName;
            public float StartTime;
            public AsyncOperation UnityAsyncOp;
            public CancellationToken CancellationToken;
            public bool IsUniTask;
            public int RetryAttempt;
            public string OperationType;
            public float LastProgressUpdate;
            public float LastProgress;
        }

        public struct AsyncLoadingFailure
        {
            public string SceneName;
            public float Timestamp;
            public string FailureType;
            public string ErrorMessage;
            public float Duration;
            public int RetryAttempt;
            public bool WasCancelled;
            public bool WasTimeout;
        }

        private void Start()
        {
            if (!enableAnalysis) return;

            Debug.Log("[AsyncLoadingAnalyzer] Starting async loading analysis");
            InvokeRepeating(nameof(PerformAnalysis), analysisInterval, analysisInterval);

            // Hook into SceneManagerBTR if available
            var sceneManagerBTR = FindObjectOfType<SceneManagerBTR>();
            if (sceneManagerBTR != null)
            {
                Debug.Log("[AsyncLoadingAnalyzer] Found SceneManagerBTR - enhanced monitoring enabled");
                HookIntoSceneManagerBTR(sceneManagerBTR);
            }
        }

        private void HookIntoSceneManagerBTR(SceneManagerBTR sceneManager)
        {
            // Monitor SceneManagerBTR operations by subscribing to its events
            try
            {
                // We'll monitor through the public interface rather than reflection
                Debug.Log("[AsyncLoadingAnalyzer] Monitoring SceneManagerBTR through public interface");

                // Start monitoring for scene loading operations
                StartCoroutine(MonitorSceneManagerBTR(sceneManager));
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AsyncLoadingAnalyzer] Could not hook into SceneManagerBTR: {e.Message}");
            }
        }

        private System.Collections.IEnumerator MonitorSceneManagerBTR(SceneManagerBTR sceneManager)
        {
            while (sceneManager != null && enableAnalysis)
            {
                // Monitor the scene manager's state periodically
                yield return new WaitForSeconds(0.5f);

                // Check if SceneManagerBTR has any public properties we can monitor
                // This is a placeholder for more specific monitoring
            }
        }

        private void PerformAnalysis()
        {
            if (!enableAnalysis) return;

            totalAnalysisTime += analysisInterval;
            AnalyzeActiveOperations();
            DetectPatterns();
            CleanupOldData();
        }

        private void AnalyzeActiveOperations()
        {
            var operationsToRemove = new List<string>();

            foreach (var kvp in activeOperations)
            {
                var operation = kvp.Value;
                float duration = Time.time - operation.StartTime;

                // Check for suspicious operations
                if (duration > suspiciousLoadTime)
                {
                    Debug.LogWarning($"[AsyncLoadingAnalyzer] Suspicious long-running operation: {operation.SceneName} ({duration:F1}s)");
                    ReportSuspiciousOperation(operation, "Long duration");
                }

                // Check Unity AsyncOperation progress
                if (operation.UnityAsyncOp != null)
                {
                    float currentProgress = operation.UnityAsyncOp.progress;

                    // Detect stalled progress
                    if (currentProgress == operation.LastProgress &&
                        Time.time - operation.LastProgressUpdate > 5f &&
                        currentProgress < 0.9f)
                    {
                        Debug.LogWarning($"[AsyncLoadingAnalyzer] Stalled progress detected: {operation.SceneName} at {currentProgress:P1}");
                        ReportSuspiciousOperation(operation, "Stalled progress");
                    }

                    // Update progress tracking
                    if (currentProgress != operation.LastProgress)
                    {
                        var updatedOp = operation;
                        updatedOp.LastProgress = currentProgress;
                        updatedOp.LastProgressUpdate = Time.time;
                        activeOperations[kvp.Key] = updatedOp;
                    }

                    // Check if operation completed
                    if (operation.UnityAsyncOp.isDone)
                    {
                        ReportOperationCompleted(operation, true);
                        operationsToRemove.Add(kvp.Key);
                    }
                }

                // Check cancellation token status
                if (trackCancellationTokens && operation.CancellationToken != default)
                {
                    if (operation.CancellationToken.IsCancellationRequested)
                    {
                        Debug.Log($"[AsyncLoadingAnalyzer] Operation cancelled: {operation.SceneName}");
                        ReportOperationCompleted(operation, false, "Cancelled");
                        operationsToRemove.Add(kvp.Key);
                        cancelledOperations++;
                    }
                    else if (duration > tokenTimeoutThreshold)
                    {
                        Debug.LogError($"[AsyncLoadingAnalyzer] Operation timeout: {operation.SceneName} ({duration:F1}s)");
                        ReportOperationCompleted(operation, false, "Timeout");
                        operationsToRemove.Add(kvp.Key);
                        timeoutOperations++;
                    }
                }
            }

            // Remove completed/failed operations
            foreach (var key in operationsToRemove)
            {
                activeOperations.Remove(key);
            }
        }

        private void DetectPatterns()
        {
            // Analyze retry patterns
            if (analyzeRetryPatterns)
            {
                foreach (var kvp in retryCounters)
                {
                    if (kvp.Value > maxRetryAttempts)
                    {
                        Debug.LogError($"[AsyncLoadingAnalyzer] Excessive retries detected for scene: {kvp.Key} ({kvp.Value} attempts)");

                        if (kvp.Value > maxFailuresForScene)
                        {
                            maxFailuresForScene = kvp.Value;
                            mostProblematicScene = kvp.Key;
                        }
                    }
                }
            }

            // Detect consecutive failure patterns
            if (recentFailures.Count >= 3)
            {
                var recentFailureWindow = recentFailures.FindAll(f => Time.time - f.Timestamp < 60f);
                if (recentFailureWindow.Count >= 3)
                {
                    Debug.LogError($"[AsyncLoadingAnalyzer] Pattern detected: {recentFailureWindow.Count} failures in last 60 seconds");
                    AnalyzeFailurePattern(recentFailureWindow);
                }
            }
        }

        private void AnalyzeFailurePattern(List<AsyncLoadingFailure> failures)
        {
            var sceneFailureCounts = new Dictionary<string, int>();
            var failureTypeCounts = new Dictionary<string, int>();

            foreach (var failure in failures)
            {
                // Count by scene
                if (!sceneFailureCounts.ContainsKey(failure.SceneName))
                    sceneFailureCounts[failure.SceneName] = 0;
                sceneFailureCounts[failure.SceneName]++;

                // Count by failure type
                if (!failureTypeCounts.ContainsKey(failure.FailureType))
                    failureTypeCounts[failure.FailureType] = 0;
                failureTypeCounts[failure.FailureType]++;
            }

            Debug.LogError($"[AsyncLoadingAnalyzer] Failure pattern analysis:");
            foreach (var kvp in sceneFailureCounts)
            {
                Debug.LogError($"  Scene '{kvp.Key}': {kvp.Value} failures");
            }
            foreach (var kvp in failureTypeCounts)
            {
                Debug.LogError($"  Type '{kvp.Key}': {kvp.Value} occurrences");
            }
        }

        private void ReportSuspiciousOperation(AsyncLoadingOperation operation, string reason)
        {
            Debug.LogWarning($"[AsyncLoadingAnalyzer] Suspicious operation: {operation.SceneName} - {reason}");

            // Additional analysis for suspicious operations
            if (operation.UnityAsyncOp != null)
            {
                Debug.LogWarning($"  Progress: {operation.UnityAsyncOp.progress:P1}, AllowSceneActivation: {operation.UnityAsyncOp.allowSceneActivation}");
                Debug.LogWarning($"  IsDone: {operation.UnityAsyncOp.isDone}, Priority: {operation.UnityAsyncOp.priority}");
            }

            if (operation.CancellationToken != default)
            {
                Debug.LogWarning($"  Token Status: Cancelled={operation.CancellationToken.IsCancellationRequested}, CanBeCancelled={operation.CancellationToken.CanBeCanceled}");
            }
        }

        private void ReportOperationCompleted(AsyncLoadingOperation operation, bool success, string failureReason = null)
        {
            totalOperationsAnalyzed++;
            float duration = Time.time - operation.StartTime;

            if (success)
            {
                successfulOperations++;
                Debug.Log($"[AsyncLoadingAnalyzer] Operation completed: {operation.SceneName} in {duration:F2}s");
            }
            else
            {
                var failure = new AsyncLoadingFailure
                {
                    SceneName = operation.SceneName,
                    Timestamp = Time.time,
                    FailureType = failureReason ?? "Unknown",
                    Duration = duration,
                    RetryAttempt = operation.RetryAttempt,
                    WasCancelled = failureReason == "Cancelled",
                    WasTimeout = failureReason == "Timeout"
                };

                recentFailures.Add(failure);
                consecutiveFailures++;
                lastFailureTime = Time.time;

                Debug.LogError($"[AsyncLoadingAnalyzer] Operation failed: {operation.SceneName} - {failureReason} (Duration: {duration:F2}s)");
            }
        }

        private void CleanupOldData()
        {
            // Remove old failures (keep last 50 or last hour)
            recentFailures.RemoveAll(f => Time.time - f.Timestamp > 3600f);
            if (recentFailures.Count > 50)
            {
                recentFailures.RemoveRange(0, recentFailures.Count - 50);
            }
        }

        // Public API for external systems to register operations
        public void RegisterAsyncOperation(string sceneName, AsyncOperation unityAsyncOp, CancellationToken cancellationToken = default, bool isUniTask = false, int retryAttempt = 0)
        {
            if (!enableAnalysis) return;

            var operation = new AsyncLoadingOperation
            {
                SceneName = sceneName,
                StartTime = Time.time,
                UnityAsyncOp = unityAsyncOp,
                CancellationToken = cancellationToken,
                IsUniTask = isUniTask,
                RetryAttempt = retryAttempt,
                OperationType = isUniTask ? "UniTask" : "Unity",
                LastProgressUpdate = Time.time,
                LastProgress = 0f
            };

            string operationKey = $"{sceneName}_{Time.time}";
            activeOperations[operationKey] = operation;

            // Track retry attempts
            if (!retryCounters.ContainsKey(sceneName))
                retryCounters[sceneName] = 0;
            retryCounters[sceneName] += retryAttempt > 0 ? 1 : 0;

            Debug.Log($"[AsyncLoadingAnalyzer] Registered operation: {sceneName} (Type: {operation.OperationType}, Retry: {retryAttempt})");
        }

        public void ReportOperationFailure(string sceneName, string errorMessage, bool wasCancelled = false)
        {
            if (!enableAnalysis) return;

            var failure = new AsyncLoadingFailure
            {
                SceneName = sceneName,
                Timestamp = Time.time,
                FailureType = wasCancelled ? "Cancelled" : "Error",
                ErrorMessage = errorMessage,
                WasCancelled = wasCancelled
            };

            recentFailures.Add(failure);
            Debug.LogError($"[AsyncLoadingAnalyzer] External failure reported: {sceneName} - {errorMessage}");
        }

        public string GetAnalysisSummary()
        {
            return $"=== Async Loading Analysis Summary ===\n" +
                   $"Total Operations Analyzed: {totalOperationsAnalyzed}\n" +
                   $"Successful: {successfulOperations}\n" +
                   $"Cancelled: {cancelledOperations}\n" +
                   $"Timeouts: {timeoutOperations}\n" +
                   $"Active Operations: {activeOperations.Count}\n" +
                   $"Recent Failures: {recentFailures.Count}\n" +
                   $"Consecutive Failures: {consecutiveFailures}\n" +
                   $"Most Problematic Scene: {mostProblematicScene} ({maxFailuresForScene} failures)\n" +
                   $"Analysis Runtime: {totalAnalysisTime:F1}s";
        }

        private void OnDestroy()
        {
            CancelInvoke();
            Debug.Log("[AsyncLoadingAnalyzer] Analysis stopped");
            Debug.Log(GetAnalysisSummary());
        }

        // Debug methods for manual testing
        [ContextMenu("Print Analysis Summary")]
        public void PrintAnalysisSummary()
        {
            Debug.Log(GetAnalysisSummary());
        }

        [ContextMenu("Simulate Operation Failure")]
        public void SimulateOperationFailure()
        {
            ReportOperationFailure("TestScene", "Simulated failure for testing", false);
        }

        [ContextMenu("Clear Analysis Data")]
        public void ClearAnalysisData()
        {
            activeOperations.Clear();
            recentFailures.Clear();
            retryCounters.Clear();
            consecutiveFailures = 0;
            Debug.Log("[AsyncLoadingAnalyzer] Analysis data cleared");
        }
    }
}

using UnityEngine;

namespace BTR
{
    /// <summary>
    /// Manager script that sets up and coordinates all scene transition diagnostic tools.
    /// Add this to your scene to automatically enable comprehensive scene transition monitoring.
    /// </summary>
    public class SceneTransitionDiagnosticsManager : MonoBehaviour
    {
        [Header("Diagnostic Components")]
        [SerializeField] private bool enableDiagnostics = true;
        [SerializeField] private bool enableMonitor = true;
        [SerializeField] private bool enableAnalyzer = true;
        [SerializeField] private bool autoSetupComponents = true;

        [Header("Integration Settings")]
        [SerializeField] private bool integrateWithSceneManagerBTR = true;
        [SerializeField] private bool integrateWithLoadingScreen = true;
        [SerializeField] private bool logIntegrationStatus = true;

        // Component References
        private SceneTransitionDiagnostics diagnostics;
        private SceneTransitionMonitor monitor;
        private AsyncLoadingAnalyzer analyzer;
        private SceneManagerBTR sceneManagerBTR;
        private LoadingScreen loadingScreen;

        private void Awake()
        {
            if (!enableDiagnostics) return;
            
            if (logIntegrationStatus)
            {
                Debug.Log("[SceneTransitionDiagnosticsManager] Initializing scene transition diagnostics");
            }
            
            SetupDiagnosticComponents();
            FindExistingComponents();
            IntegrateWithExistingSystems();
        }

        private void SetupDiagnosticComponents()
        {
            if (!autoSetupComponents) return;

            // Setup SceneTransitionDiagnostics
            if (enableDiagnostics)
            {
                diagnostics = GetComponent<SceneTransitionDiagnostics>();
                if (diagnostics == null)
                {
                    diagnostics = gameObject.AddComponent<SceneTransitionDiagnostics>();
                    if (logIntegrationStatus)
                    {
                        Debug.Log("[SceneTransitionDiagnosticsManager] Added SceneTransitionDiagnostics component");
                    }
                }
            }

            // Setup SceneTransitionMonitor
            if (enableMonitor)
            {
                monitor = GetComponent<SceneTransitionMonitor>();
                if (monitor == null)
                {
                    monitor = gameObject.AddComponent<SceneTransitionMonitor>();
                    if (logIntegrationStatus)
                    {
                        Debug.Log("[SceneTransitionDiagnosticsManager] Added SceneTransitionMonitor component");
                    }
                }
            }

            // Setup AsyncLoadingAnalyzer
            if (enableAnalyzer)
            {
                analyzer = GetComponent<AsyncLoadingAnalyzer>();
                if (analyzer == null)
                {
                    analyzer = gameObject.AddComponent<AsyncLoadingAnalyzer>();
                    if (logIntegrationStatus)
                    {
                        Debug.Log("[SceneTransitionDiagnosticsManager] Added AsyncLoadingAnalyzer component");
                    }
                }
            }
        }

        private void FindExistingComponents()
        {
            // Find SceneManagerBTR
            if (integrateWithSceneManagerBTR)
            {
                sceneManagerBTR = FindObjectOfType<SceneManagerBTR>();
                if (sceneManagerBTR != null)
                {
                    if (logIntegrationStatus)
                    {
                        Debug.Log("[SceneTransitionDiagnosticsManager] Found SceneManagerBTR for integration");
                    }
                }
                else
                {
                    Debug.LogWarning("[SceneTransitionDiagnosticsManager] SceneManagerBTR not found in scene");
                }
            }

            // Find LoadingScreen
            if (integrateWithLoadingScreen)
            {
                loadingScreen = FindObjectOfType<LoadingScreen>();
                if (loadingScreen != null)
                {
                    if (logIntegrationStatus)
                    {
                        Debug.Log("[SceneTransitionDiagnosticsManager] Found LoadingScreen for integration");
                    }
                }
                else if (logIntegrationStatus)
                {
                    Debug.Log("[SceneTransitionDiagnosticsManager] LoadingScreen not found in scene");
                }
            }
        }

        private void IntegrateWithExistingSystems()
        {
            // Integration with SceneManagerBTR
            if (sceneManagerBTR != null && analyzer != null)
            {
                // The analyzer will automatically detect and monitor SceneManagerBTR
                if (logIntegrationStatus)
                {
                    Debug.Log("[SceneTransitionDiagnosticsManager] Integrated with SceneManagerBTR");
                }
            }

            // Integration with LoadingScreen
            if (loadingScreen != null)
            {
                // Monitor loading screen operations
                if (logIntegrationStatus)
                {
                    Debug.Log("[SceneTransitionDiagnosticsManager] Integrated with LoadingScreen");
                }
            }
        }

        // Public API for external systems to report events
        public void ReportSceneTransitionStarted(string sceneName)
        {
            if (!enableDiagnostics) return;
            
            monitor?.ReportTransitionStarted(sceneName);
            diagnostics?.LogCustomEvent("External", $"Transition started: {sceneName}");
        }

        public void ReportSceneTransitionFailed(string sceneName, string reason)
        {
            if (!enableDiagnostics) return;
            
            monitor?.ReportTransitionFailed(sceneName, reason);
            analyzer?.ReportOperationFailure(sceneName, reason);
            diagnostics?.ReportTransitionFailure(sceneName, reason);
        }

        public void ReportAsyncOperationStarted(string sceneName, UnityEngine.AsyncOperation asyncOp, System.Threading.CancellationToken cancellationToken = default)
        {
            if (!enableDiagnostics) return;
            
            analyzer?.RegisterAsyncOperation(sceneName, asyncOp, cancellationToken, false);
            monitor?.ReportAsyncOperationStarted($"Scene Load: {sceneName}");
        }

        public void ReportUniTaskOperationStarted(string sceneName, System.Threading.CancellationToken cancellationToken = default)
        {
            if (!enableDiagnostics) return;
            
            analyzer?.RegisterAsyncOperation(sceneName, null, cancellationToken, true);
            monitor?.ReportAsyncOperationStarted($"UniTask: {sceneName}");
        }

        // Debug and utility methods
        [ContextMenu("Print Diagnostic Summary")]
        public void PrintDiagnosticSummary()
        {
            if (diagnostics != null)
            {
                Debug.Log(diagnostics.GetDiagnosticSummary());
            }
            
            if (analyzer != null)
            {
                Debug.Log(analyzer.GetAnalysisSummary());
            }
            
            if (monitor != null)
            {
                // Monitor will print its own summary
                Debug.Log("Monitor summary printed to on-screen display");
            }
        }

        [ContextMenu("Test Scene Transition Failure")]
        public void TestSceneTransitionFailure()
        {
            ReportSceneTransitionFailed("TestScene", "Simulated failure for testing");
        }

        [ContextMenu("Clear All Diagnostic Data")]
        public void ClearAllDiagnosticData()
        {
            if (analyzer != null)
            {
                analyzer.ClearAnalysisData();
            }
            
            Debug.Log("[SceneTransitionDiagnosticsManager] All diagnostic data cleared");
        }

        // Status reporting
        public bool IsMonitoringActive()
        {
            return enableDiagnostics && (diagnostics != null || monitor != null || analyzer != null);
        }

        public string GetIntegrationStatus()
        {
            var status = "=== Scene Transition Diagnostics Integration Status ===\n";
            status += $"Diagnostics Enabled: {enableDiagnostics}\n";
            status += $"SceneTransitionDiagnostics: {(diagnostics != null ? "Active" : "Inactive")}\n";
            status += $"SceneTransitionMonitor: {(monitor != null ? "Active" : "Inactive")}\n";
            status += $"AsyncLoadingAnalyzer: {(analyzer != null ? "Active" : "Inactive")}\n";
            status += $"SceneManagerBTR Integration: {(sceneManagerBTR != null ? "Connected" : "Not Found")}\n";
            status += $"LoadingScreen Integration: {(loadingScreen != null ? "Connected" : "Not Found")}\n";
            return status;
        }

        private void OnValidate()
        {
            // Ensure components are properly configured when settings change in inspector
            if (Application.isPlaying) return;
            
            if (!enableDiagnostics)
            {
                enableMonitor = false;
                enableAnalyzer = false;
            }
        }

        private void Start()
        {
            if (logIntegrationStatus)
            {
                Debug.Log(GetIntegrationStatus());
            }
        }

        private void OnDestroy()
        {
            if (logIntegrationStatus)
            {
                Debug.Log("[SceneTransitionDiagnosticsManager] Scene transition diagnostics manager destroyed");
            }
        }
    }
}

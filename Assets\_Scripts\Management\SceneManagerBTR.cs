using UnityEngine;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using UnityEngine.SceneManagement;
using UnityEngine.Events;
using System.Linq;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
// using JPG.Universal;
using BTR;
using PrimeTween;
using static AsyncOperationExtensions;
using Cysharp.Threading.Tasks;
using System.Threading;
using Unity.Profiling;
using System.Runtime.CompilerServices;
using ZLinq;

namespace BTR
{
    /// <summary>
    /// Manages scene loading, transitions, and optimization in the BTR game.
    /// Implements performance monitoring, error recovery, and memory optimization.
    /// </summary>
    public class SceneManagerBTR : MonoBehaviour
    {
        #region Performance Monitoring
        /// <summary>
        /// Stores performance metrics for scene loading operations.
        /// Tracks load times, memory usage, and frame time impact.
        /// </summary>
        private readonly Dictionary<string, float> _sceneLoadTimes = new();
        private readonly ProfilerMarker _sceneLoadMarker = new("Scene Loading");
        private readonly ProfilerMarker _sceneTransitionMarker = new("Scene Transition");
        private readonly ProfilerMarker _initializationMarker = new("Scene Initialization");

        [System.Serializable]
        public struct ScenePerformanceMetrics
        {
            /// <summary>Time taken to load the scene in seconds</summary>
            public float LoadTime;
            /// <summary>Total time including transitions</summary>
            public float TransitionTime;
            /// <summary>Memory usage before loading in MB</summary>
            public int MemoryUsageBefore;
            /// <summary>Memory usage after loading in MB</summary>
            public int MemoryUsageAfter;
            /// <summary>Impact on frame time in seconds</summary>
            public float FrameTimeImpact;
        }

        private readonly Dictionary<string, ScenePerformanceMetrics> _performanceMetrics = new();
        #endregion

        #region Progress Reporting
        /// <summary>
        /// Detailed progress information for scene loading operations.
        /// Provides granular progress updates and operation status.
        /// </summary>
        public struct SceneLoadProgress
        {
            /// <summary>Overall load progress from 0 to 1</summary>
            public float LoadProgress;
            /// <summary>Asset loading progress from 0 to 1</summary>
            public float AssetProgress;
            /// <summary>Current operation description</summary>
            public string CurrentOperation;
            /// <summary>Whether a scene transition is in progress</summary>
            public bool IsTransitioning;
            /// <summary>Name of the scene being loaded</summary>
            public string SceneName;

            /// <summary>
            /// Creates a new progress update with the specified parameters.
            /// </summary>
            public static SceneLoadProgress Create(string sceneName, float loadProgress, float assetProgress, string operation)
            {
                return new SceneLoadProgress
                {
                    SceneName = sceneName,
                    LoadProgress = loadProgress,
                    AssetProgress = assetProgress,
                    CurrentOperation = operation,
                    IsTransitioning = false
                };
            }
        }

        private readonly Queue<string> _operationLog = new();
        private const int MAX_LOG_ENTRIES = 50;
        #endregion

        public static SceneManagerBTR Instance { get; private set; }

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            Instance = null;
        }
        [SerializeField] private Volume globalVolume;
        [SerializeField] private WaveEventChannel waveEventChannel;
        [SerializeField] private string currentSongSectionDisplay; // For debugging in inspector
        [SerializeField] public bool useLoadingScreen = true; // Toggle for loading screen usage
        [Header("Debug Logging")]
        [SerializeField] private bool enableDebugLogs = false;
        [SerializeField] private bool showSceneLoadLogs = false;
        [SerializeField] private bool showSectionChangeLogs = false;
        [SerializeField] private bool showWaveSystemLogs = false;
        [SerializeField] private string logCategory = "SCENE_LOAD"; // Use SCENE_LOAD category for our logs

        private SceneEvents sceneEvents => SceneEventsManager.Instance?.Events;
        private GameEvents gameEvents => GameEventsManager.Instance?.Events;
        private readonly CancellationTokenSource _destroyToken = new CancellationTokenSource();

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                SubscribeToEvents();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        private void OnDestroy()
        {
            _destroyToken.Cancel();
            _destroyToken.Dispose();
            UnsubscribeFromEvents();
        }

        private void SubscribeToEvents()
        {
            if (sceneEvents == null)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] SceneEvents not found! Make sure SceneEventsManager is in the scene with assigned SceneEvents asset.");
                return;
            }

            if (waveEventChannel == null)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] WaveEventChannel not assigned to SceneManagerBTR!");
                return;
            }

            sceneEvents.OnSceneLoadRequested += LoadScene;
            sceneEvents.OnSceneLoadStarted += HandleSceneLoadStarted;
            sceneEvents.OnSceneLoadCompleted += HandleSceneLoadCompleted;

            waveEventChannel.OnWaveStarted += HandleWaveStarted;
            waveEventChannel.OnWaveCompleted += HandleWaveCompleted;
            waveEventChannel.OnWaveCustomEvent += HandleWaveCustomEvent;
        }

        private void UnsubscribeFromEvents()
        {
            if (SceneEventsManager.Instance != null && sceneEvents != null)
            {
                sceneEvents.OnSceneLoadRequested -= LoadScene;
                sceneEvents.OnSceneLoadStarted -= HandleSceneLoadStarted;
                sceneEvents.OnSceneLoadCompleted -= HandleSceneLoadCompleted;
            }

            if (waveEventChannel != null)
            {
                waveEventChannel.OnWaveStarted -= HandleWaveStarted;
                waveEventChannel.OnWaveCompleted -= HandleWaveCompleted;
                waveEventChannel.OnWaveCustomEvent -= HandleWaveCustomEvent;
            }

            if (splineManager != null)
            {
                splineManager.OnFinalSplineReached -= HandleFinalSplineReached;
            }
        }

        public SceneGroup currentGroup;

        [SerializeField]
        private string baseSceneName;
        private Scene baseScene;
        private Scene currentAdditiveScene;
        private bool _isLoadingScene = false;
        private int currentSceneIndex;
        private int currentSectionIndex;
        private SplineManager splineManager;
        private bool isTransitioning = false;
        private int expectedWaves = 0;
        private int completedWaves = 0;
        private bool isFirstUpdate = true;
        private int currentWaveCount;

        [SerializeField] private GameObject vfxManagerPrefab;

        private bool isHandlingSceneLoad = false;
        private float lastSceneChangeTime = 0f;
        private const float SCENE_CHANGE_COOLDOWN = 1f; // Reduced from 5s to 1s

        #region Error Recovery
        /// <summary>
        /// Configuration for scene loading retry behavior.
        /// Controls retry attempts, delays, and progressive backoff.
        /// </summary>
        [System.Serializable]
        public class RetryConfiguration
        {
            /// <summary>Maximum number of retry attempts</summary>
            public int MaxRetries = 3;
            /// <summary>Base delay between retries in seconds</summary>
            public float RetryDelay = 1f;
            /// <summary>Whether to increase delay between retries</summary>
            public bool EnableProgressiveDelay = true;
            /// <summary>Multiplier for progressive delay increase</summary>
            public float ProgressiveDelayMultiplier = 1.5f;
        }

        [SerializeField] private RetryConfiguration retryConfig = new();

        /// <summary>
        /// Attempts to load a scene with retry logic on failure.
        /// Implements progressive delay between retries and detailed progress reporting.
        /// </summary>
        /// <param name="sceneName">Name of the scene to load</param>
        /// <param name="progress">Optional progress reporter</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if scene was loaded successfully, false if all retries failed</returns>
        private async UniTask<bool> TryLoadSceneWithRetry(string sceneName, IProgress<SceneLoadProgress> progress = null, CancellationToken cancellationToken = default)
        {
            float currentDelay = retryConfig.RetryDelay;

            for (int attempt = 0; attempt < retryConfig.MaxRetries; attempt++)
            {
                try
                {
                    using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                    if (attempt > 0)
                    {
                        LogOperation($"Retry attempt {attempt + 1}/{retryConfig.MaxRetries} for scene {sceneName}", logCategory);
                        progress?.Report(SceneLoadProgress.Create(
                            sceneName,
                            0f,
                            0f,
                            $"Retrying load ({attempt + 1}/{retryConfig.MaxRetries})"
                        ));
                    }

                    await LoadSceneAsync(sceneName, linkedCts.Token);
                    return true;
                }
                catch (OperationCanceledException)
                {
                    throw; // Propagate cancellation
                }
                catch (Exception e) when (attempt < retryConfig.MaxRetries - 1)
                {
                    LogOperation($"Attempt {attempt + 1} failed: {e.Message}", logCategory);
                    if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] [SceneManager] Load attempt {attempt + 1}/{retryConfig.MaxRetries} failed for {sceneName}: {e.Message}");

                    // Wait before retrying
                    try
                    {
                        progress?.Report(SceneLoadProgress.Create(
                            sceneName,
                            0f,
                            0f,
                            $"Waiting {currentDelay:F1}s before retry {attempt + 2}/{retryConfig.MaxRetries}"
                        ));

                        await UniTask.Delay(TimeSpan.FromSeconds(currentDelay), cancellationToken: cancellationToken);

                        if (retryConfig.EnableProgressiveDelay)
                        {
                            currentDelay *= retryConfig.ProgressiveDelayMultiplier;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        LogOperation($"Retry cancelled for scene {sceneName}", logCategory);
                        throw;
                    }
                }
            }

            LogOperation($"All retry attempts failed for scene {sceneName}", logCategory);
            return false;
        }

        public void LoadSceneWithRetry(string sceneName)
        {
            if (string.IsNullOrEmpty(sceneName))
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Scene name cannot be null or empty");
                return;
            }

            _ = TryLoadSceneWithRetry(sceneName).ContinueWith(success =>
            {
                if (!success)
                {
                    if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] [SceneManager] Failed to load scene {sceneName} after {retryConfig.MaxRetries} attempts");
                    gameEvents?.TriggerSceneLoadProgress(-1f);
                }
            });
        }
        #endregion

        #region Memory Management
        /// <summary>
        /// Configuration for memory optimization and scene preloading.
        /// Controls memory thresholds, cleanup intervals, and preloading behavior.
        /// </summary>
        [System.Serializable]
        public class MemoryOptimizationConfig
        {
            /// <summary>Whether to enable scene preloading</summary>
            public bool EnablePreloading = true;
            /// <summary>Whether to force GC during cleanup</summary>
            public bool AggressiveGarbageCollection = false;
            /// <summary>Memory threshold in MB to trigger cleanup</summary>
            public int MemoryThresholdMB = 1024;
            /// <summary>Interval between memory cleanup operations</summary>
            public float UnloadUnusedAssetsInterval = 60f;
        }

        [SerializeField] private MemoryOptimizationConfig memoryConfig = new();
        private float _lastMemoryCleanup;
        private readonly HashSet<string> _preloadedScenes = new();

        /// <summary>
        /// Preloads a scene in the background without activating it.
        /// Helps reduce loading times during actual scene transitions.
        /// </summary>
        /// <param name="sceneName">Name of the scene to preload</param>
        /// <param name="cancellationToken">Cancellation token</param>
        private async UniTask PreloadSceneAsync(string sceneName, CancellationToken cancellationToken = default)
        {
            if (!memoryConfig.EnablePreloading || _preloadedScenes.Contains(sceneName))
            {
                return;
            }

            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);
                LogOperation($"Preloading scene: {sceneName}", logCategory);

                var asyncLoad = SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
                asyncLoad.allowSceneActivation = false; // Don't activate the scene yet

                while (asyncLoad.progress < 0.9f)
                {
                    linkedCts.Token.ThrowIfCancellationRequested();
                    await UniTask.Yield(PlayerLoopTiming.Update, linkedCts.Token);
                }

                _preloadedScenes.Add(sceneName);
                LogOperation($"Scene preloaded: {sceneName}", logCategory);
            }
            catch (OperationCanceledException)
            {
                LogOperation($"Preload cancelled for scene: {sceneName}", logCategory);
                throw;
            }
            catch (Exception e)
            {
                LogOperation($"Failed to preload scene {sceneName}: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] [SceneManager] Preload failed for {sceneName}: {e.Message}");
            }
        }

        private async UniTask CheckMemoryThreshold(CancellationToken cancellationToken = default)
        {
            var currentMemory = GC.GetTotalMemory(false) / 1024 / 1024; // Convert to MB
            if (currentMemory > memoryConfig.MemoryThresholdMB)
            {
                LogOperation($"Memory threshold exceeded: {currentMemory}MB > {memoryConfig.MemoryThresholdMB}MB", logCategory);
                await CleanupMemoryAsync(cancellationToken);
            }
        }

        /// <summary>
        /// Performs memory cleanup when threshold is exceeded.
        /// Includes garbage collection and asset unloading based on configuration.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        private async UniTask CleanupMemoryAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                LogOperation("Starting memory cleanup", logCategory);
                var memoryBefore = GC.GetTotalMemory(false) / 1024 / 1024;

                // Force garbage collection if configured
                if (memoryConfig.AggressiveGarbageCollection)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }

                // Unload unused assets
                await Resources.UnloadUnusedAssets().ToUniTask(cancellationToken: cancellationToken);

                var memoryAfter = GC.GetTotalMemory(false) / 1024 / 1024;
                LogOperation($"Memory cleanup complete: Freed {memoryBefore - memoryAfter}MB", logCategory);
                _lastMemoryCleanup = Time.time;
            }
            catch (OperationCanceledException)
            {
                LogOperation("Memory cleanup cancelled", logCategory);
                throw;
            }
            catch (Exception e)
            {
                LogOperation($"Error during memory cleanup: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] [SceneManager] Memory cleanup failed: {e.Message}");
            }
        }

        private async UniTask PreloadNextScene(CancellationToken cancellationToken = default)
        {
            if (!memoryConfig.EnablePreloading || currentGroup == null)
            {
                return;
            }

            var nextSceneIndex = (currentSceneIndex + 1) % currentGroup.scenes.Length;
            var nextSceneName = currentGroup.scenes[nextSceneIndex].sceneName;

            await PreloadSceneAsync(nextSceneName, cancellationToken);
        }

        private void Update()
        {
            // Check memory usage periodically
            if (Time.time - _lastMemoryCleanup > memoryConfig.UnloadUnusedAssetsInterval)
            {
                _ = CheckMemoryThreshold()
                    .ContinueWith(async () =>
                    {
                        await PreloadNextScene();
                    });
            }
        }
        #endregion

        private async void Start()
        {
            try
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Starting initialization...");

                if (currentGroup == null)
                {
                    if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Current group is null! Please assign a SceneGroup in the inspector.");
                    return;
                }

                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Current group: {currentGroup.name}, Scene count: {currentGroup.scenes.Length}");

                InitializeNextSceneValues();

                // Defer SplineManager lookup to avoid blocking startup
                StartCoroutine(InitializeSplineManagerAsync());

                await InitializeScenes().AttachExternalCancellation(_destroyToken.Token);
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Initialization cancelled");
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error during initialization: {e.Message}");
            }
        }

        private System.Collections.IEnumerator InitializeSplineManagerAsync()
        {
            // Wait a frame to avoid blocking startup
            yield return null;

            splineManager = FindFirstObjectByType<SplineManager>();
            if (splineManager != null)
            {
                splineManager.OnFinalSplineReached += HandleFinalSplineReached;
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] SplineManager found and subscribed");
            }
            else
            {
                if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] SplineManager not found!");
            }
        }

        public async UniTask InitializeScenes(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                await LoadBaseSceneAsync(linkedCts.Token);

                bool hasExistingScenes = await TryUseOpenOuroborosSceneAsync(linkedCts.Token);

                if (LoadingScreen.Instance != null)
                {
                    if (hasExistingScenes)
                    {
                        LoadingScreen.Instance.InitializeForExistingScenes();
                    }
                    else
                    {
                        LoadingScreen.Instance.InitializeForFreshStart();
                    }
                }

                if (!hasExistingScenes)
                {
                    currentSceneIndex = 0;
                    currentSectionIndex = 0;
                    await LoadFirstAdditiveSceneAsync(linkedCts.Token);
                }

                currentWaveCount = 0;
                isFirstUpdate = true;
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene initialization cancelled");
                throw;
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error during scene initialization: {e.Message}");
                throw;
            }
        }

        public async UniTask LoadFirstAdditiveSceneAsync(CancellationToken cancellationToken = default)
        {
            if (currentGroup == null || currentGroup.scenes.Length == 0)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] No scenes defined in the current group.");
                return;
            }

            string firstSceneName = currentGroup.scenes[0].sceneName;
            await LoadAdditiveSceneAsync(firstSceneName, null, cancellationToken);
        }

        public async UniTask LoadAdditiveSceneAsync(string sceneName, IProgress<float> progressReporter = null, CancellationToken cancellationToken = default)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Starting additive scene load");

            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                sceneEvents?.TriggerSceneLoadStarted(sceneName);

                if (currentAdditiveScene.IsValid() && currentAdditiveScene.isLoaded)
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Unloading current additive scene");
                    await UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(currentAdditiveScene)
                        .ToUniTask(cancellationToken: linkedCts.Token);
                }

                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Loading new scene: {sceneName}");
                var asyncLoad = SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive);
                asyncLoad.allowSceneActivation = true; // Force immediate activation
                float currentProgress = 0f;

                while (!asyncLoad.isDone)
                {
                    linkedCts.Token.ThrowIfCancellationRequested();
                    currentProgress = Mathf.Clamp01(asyncLoad.progress / 0.9f);

                    var progress = SceneLoadProgress.Create(
                        sceneName,
                        currentProgress,
                        asyncLoad.progress,
                        $"Loading scene assets ({(currentProgress * 100):F0}%)"
                    );

                    gameEvents?.TriggerSceneLoadProgress(currentProgress);
                    LogOperation($"Load progress: {currentProgress:P0}", logCategory);

                    await UniTask.Yield(PlayerLoopTiming.Update, linkedCts.Token);
                }

                currentAdditiveScene = SceneManager.GetSceneByName(sceneName);
                if (!currentAdditiveScene.IsValid())
                {
                    throw new Exception($"Failed to load scene: {sceneName}");
                }

                SceneManager.SetActiveScene(currentAdditiveScene);
                ScoreManager.Instance.CurrentSceneWaveCount = 0;
                OnSceneLoaded(currentAdditiveScene, LoadSceneMode.Additive);

                sceneEvents?.TriggerSceneLoadCompleted(sceneName);

                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene loaded, starting fade out");
                if (useLoadingScreen && LoadingScreen.Instance != null)
                {
                    try
                    {
                        await LoadingScreen.Instance.StartFadeOut(progressReporter, linkedCts.Token);
                    }
                    catch (Exception e)
                    {
                        if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] Fade out failed, proceeding without fade: {e.Message}");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Additive scene load cancelled for {sceneName}");
                throw;
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error loading scene: {e.Message}");
                throw;
            }
        }

        public void updateStatus(string waveEvent)
        {
            // Forward all events to HandleWaveCustomEvent for consistent handling
            HandleWaveCustomEvent(waveEvent, 0);
        }

        private void MoveToNextSection()
        {
            try
            {
                var currentScene = currentGroup.scenes[currentSceneIndex];
                var oldSection = currentScene.songSections[currentSectionIndex];

                currentSectionIndex++;
                completedWaves = 0;

                if (currentSectionIndex >= currentScene.songSections.Length)
                {
                    currentSectionIndex = currentScene.songSections.Length - 1;
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Reached end of sections, staying on last section");
                    return;
                }

                var newSection = currentScene.songSections[currentSectionIndex];
                expectedWaves = newSection.waves;

                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Moving from section {oldSection.name} to {newSection.name}:");
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}]   - Old section value: {oldSection.section}");
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}]   - New section value: {newSection.section}");
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}]   - Expected waves: {expectedWaves}");

                // Only update music if the section value actually changed
                if (Math.Abs(oldSection.section - newSection.section) > 0.01f)
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Section value changed, updating music");
                    UpdateMusicSection();
                }
                else
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Section value unchanged, skipping music update");
                }

                waveEventChannel.TriggerSectionStarted(newSection.name, expectedWaves);

                // Increment spline when moving to a new section
                if (splineManager != null)
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Incrementing spline for section {newSection.name}");
                    splineManager.IncrementSpline();

                    // Verify spline incrementation was successful and force position reset for transitions
                    if (newSection.waves == 0)
                    {
                        if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Detected transition section, forcing spline verification");
                        splineManager.ForceVerifyAndReset();
                    }

                    // Verify that spline increment was successful
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] After increment call - SplineManager currSpline: {(splineManager as SplineManager)?.currSpline ?? -1}");
                }
                else
                {
                    if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] SplineManager is null, cannot increment spline!");
                }

                // For 0-wave sections (transitions), we'll wait for the next wave start
                if (expectedWaves == 0)
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] In transition section {newSection.name}, waiting for next wave start to advance");
                }
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error moving to next section: {e.Message}");
            }
        }

        private void UpdateMusicSection()
        {
            try
            {
                if (enableDebugLogs && showSectionChangeLogs)
                {
                    Debug.Log($"[{GetType().Name}] Updating music section for scene {currentGroup.scenes[currentSceneIndex].sceneName}:");
                    Debug.Log($"[{GetType().Name}]   - Scene Index: {currentSceneIndex}");
                    Debug.Log($"[{GetType().Name}]   - Section Index: {currentSectionIndex}");
                    Debug.Log($"[{GetType().Name}]   - Section Value: {CurrentSection.section}");
                    Debug.Log($"[{GetType().Name}]   - Section Name: {CurrentSection.name}");
                }

                if (AudioManager.Instance == null)
                {
                    if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] AudioManager.Instance is null!");
                    return;
                }

                var currentScene = currentGroup.scenes[currentSceneIndex];
                var currentSection = currentScene.songSections[currentSectionIndex];

                AudioManager.Instance.ChangeSongSection(currentGroup, currentSceneIndex, currentSection.section);
                AudioManager.Instance.ApplyMusicChanges(currentGroup, currentSceneIndex, currentSection.section);

                currentSongSectionDisplay = $"Scene {currentSceneIndex} - {currentSection.name} (Value: {currentSection.section})";
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error updating music section: {e.Message}");
            }
        }

        private void HandleWaveCustomEvent(string eventType, int waveNumber)
        {
            try
            {
                if (string.IsNullOrEmpty(eventType)) return;

                if (showWaveSystemLogs)
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Handling wave event: {eventType} in section {CurrentSection.name} ({currentSectionIndex}/{currentWaveCount} waves)");
                }

                switch (eventType.ToLower())
                {
                    case "switch scene":
                        if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Received Switch Scene event, initiating scene transition");
                        _ = ChangeSceneWithTransitionToNextAsync().AttachExternalCancellation(_destroyToken.Token);
                        break;

                    case "wavestart":
                        if (isFirstUpdate)
                        {
                            isFirstUpdate = false;
                            expectedWaves = CurrentSection.waves;
                            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] First wave started in section {CurrentSection.name} (Expected waves: {expectedWaves})");

                            // Update music and trigger section started regardless of wave count
                            UpdateMusicSection();
                            waveEventChannel.TriggerSectionStarted(CurrentSection.name, expectedWaves);

                            // Only move to next section if this is a 0-wave section AND we're not in the last section
                            if (expectedWaves == 0 && currentSectionIndex < CurrentScene.songSections.Length - 1)
                            {
                                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Starting in 0-wave section, moving to next section");
                                MoveToNextSection();
                            }
                        }
                        else if (CurrentSection.waves == 0 && currentSectionIndex < CurrentScene.songSections.Length - 1)
                        {
                            // This is a critical case - we're in a transition section and need to move to the next section
                            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Wave started while in transition section {CurrentSection.name}, moving to next section");
                            MoveToNextSection();

                            // Additional safety check - make sure spline was properly incremented during transition
                            if (splineManager != null)
                            {
                                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Verifying spline increment after transition section");
                                // Force spline verification to ensure player is on the right track
                                // This is needed because there might be timing issues with the transitions
                                splineManager.ForceVerifyAndReset();
                            }
                        }
                        break;

                    case "waveend":
                        if (expectedWaves > 0)  // Only process wave end if we expect waves
                        {
                            completedWaves++;
                            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Wave completed in section {CurrentSection.name} ({completedWaves}/{expectedWaves})");

                            if (completedWaves >= expectedWaves)
                            {
                                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Section {CurrentSection.name} completed, moving to next section");
                                waveEventChannel.TriggerSectionCompleted();
                                _ = MoveToNextSectionOrSceneAsync().AttachExternalCancellation(_destroyToken.Token);
                            }
                        }
                        break;

                    default:
                        if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Unhandled wave event type: {eventType}");
                        break;
                }
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error handling wave event: {e.Message}");
            }
        }

        private void HandleWaveStarted(int waveNumber)
        {
            // Only track wave count, don't handle section transitions here
            currentWaveCount++;
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Wave {waveNumber} started (Total waves: {currentWaveCount})");
        }

        private void HandleWaveCompleted(int waveNumber)
        {
            // Wave completion is now handled entirely through HandleWaveCustomEvent("waveend")
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Wave {waveNumber} completed");
        }

        private void HandleFinalSplineReached()
        {
            // Don't trigger another scene change if we're already transitioning
            if (isTransitioning || Time.time - lastSceneChangeTime < SCENE_CHANGE_COOLDOWN)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Skipping spline-triggered scene change due to recent transition");
                return;
            }

            try
            {
                _ = ChangeSceneWithTransitionToNextAsync().AttachExternalCancellation(_destroyToken.Token);
            }
            catch (Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error handling final spline: {e.Message}");
            }
        }

        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] OnSceneLoaded called for scene: {scene.name}, Mode: {mode}");

            if (scene != currentAdditiveScene)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Ignoring scene load for {scene.name} as it's not the current additive scene");
                return;
            }

            _isLoadingScene = false;

            UpdateSceneAttributes();

            // Initialize music but don't change section yet - wait for first wave
            if (AudioManager.Instance != null)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Initializing audio manager on scene load for scene index {currentSceneIndex}");
                float section = currentGroup.scenes[currentSceneIndex].songSections[currentSectionIndex].section;
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Setting music section to {section} for scene {scene.name}");

                // Update display
                string sectionName = currentGroup.scenes[currentSceneIndex].songSections[currentSectionIndex].name;
                currentSongSectionDisplay = $"Scene {currentSceneIndex} - {sectionName} (Value: {section})";

                // Force both music update methods
                AudioManager.Instance.ChangeSongSection(currentGroup, currentSceneIndex, section);
                AudioManager.Instance.ApplyMusicChanges(currentGroup, currentSceneIndex, section);
            }
            else
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] [SceneManager] AudioManager.Instance is null in OnSceneLoaded!");
                currentSongSectionDisplay = "ERROR: AudioManager is null";
            }

            // Make sure SplineManager is properly initialized
            if (splineManager != null)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Initializing SplineManager after scene load");
                splineManager.InitializeSplineSystem();
            }
            else
            {
                if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] [SceneManager] SplineManager is null in OnSceneLoaded, deferring lookup");
                // Defer the lookup to avoid blocking scene loading
                StartCoroutine(FindAndInitializeSplineManagerAsync());
            }

            LogCurrentState();

            // Reduce effect transition times
            GlobalVolumeManager.Instance.TransitionEffectIn(0.5f);
            GlobalVolumeManager.Instance.TransitionEffectOut(0.5f);

            GameManager.Instance.InitializeListenersAndComponents(scene, mode);
        }

        private System.Collections.IEnumerator FindAndInitializeSplineManagerAsync()
        {
            // Wait a frame to avoid blocking scene loading
            yield return null;

            splineManager = FindFirstObjectByType<SplineManager>();
            if (splineManager != null)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Found SplineManager, initializing");
                splineManager.InitializeSplineSystem();
                splineManager.OnFinalSplineReached += HandleFinalSplineReached;
            }
            else
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] [SceneManager] SplineManager not found in scene!");
            }
        }

        private void UpdateSceneAttributes()
        {
            if (currentGroup != null && currentSceneIndex < currentGroup.scenes.Length)
            {
                var sceneData = currentGroup.scenes[currentSceneIndex];
                if (sceneData.songSections.Length > 0)
                {
                    var songSection = sceneData.songSections[currentSectionIndex];
                    
                    // Safely update wave count if ScoreManager is available
                    if (ScoreManager.Instance != null)
                    {
                        ScoreManager.Instance.CurrentSceneWaveCount = songSection.waves;
                    }
                    else if (enableDebugLogs)
                    {
                        Debug.LogWarning($"[{GetType().Name}] ScoreManager.Instance is null in UpdateSceneAttributes");
                    }
                }
            }
        }

        private void LogCurrentState()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] <color=cyan>[SCENE] Current State - Scene: {currentSceneIndex}, Section: {currentSectionIndex}, Wave: {currentWaveCount}</color>");
            }
        }

        public void InitializeNextSceneValues()
        {
            if (currentGroup == null || currentGroup.scenes.Length == 0)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] CurrentGroup is null or has no scenes.");
                return;
            }
            // Add any additional initialization logic here if needed
        }

        public int GetCurrentWaveCount()
        {
            if (currentGroup != null && currentSceneIndex < currentGroup.scenes.Length)
            {
                var sceneData = currentGroup.scenes[currentSceneIndex];
                if (sceneData.songSections.Length > 0)
                {
                    return sceneData.songSections[currentSectionIndex].waves;
                }
            }
            return 0;
        }

        public async UniTask RestartGame(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                // Reset scene indices
                currentSceneIndex = 0;
                currentSectionIndex = 0;
                completedWaves = 0;

                // Trigger game restart event
                gameEvents.TriggerGameRestart();

                // Reset music state by forcing initial section
                if (currentGroup != null && currentGroup.scenes.Length > 0)
                {
                    expectedWaves = currentGroup.scenes[0].songSections[0].waves;
                }

                // Load first scene
                await LoadFirstAdditiveSceneAsync(linkedCts.Token);
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Game restart cancelled");
                throw;
            }
        }

        public async UniTask UnloadAllAdditiveScenes(CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                int sceneCount = SceneManager.sceneCount;
                for (int i = 0; i < sceneCount; i++)
                {
                    linkedCts.Token.ThrowIfCancellationRequested();

                    Scene scene = SceneManager.GetSceneAt(i);
                    if (scene != SceneManager.GetActiveScene() && scene.name != baseSceneName)
                    {
                        await SceneManager.UnloadSceneAsync(scene).ToUniTask(cancellationToken: linkedCts.Token);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Scene unloading cancelled");
                throw;
            }
        }

        private async UniTask<bool> TryUseOpenOuroborosSceneAsync(CancellationToken cancellationToken = default)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Starting scene initialization check...");

            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                for (int i = 0; i < SceneManager.sceneCount; i++)
                {
                    Scene openScene = SceneManager.GetSceneAt(i);
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Checking scene: {openScene.name}, IsLoaded: {openScene.isLoaded}");

                    if (openScene.isLoaded && IsSceneInOuroborosGroup(openScene.name))
                    {
                        if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Found existing Ouroboros scene: {openScene.name}");
                        currentAdditiveScene = openScene;
                        bool setActiveSuccess = SceneManager.SetActiveScene(currentAdditiveScene);

                        if (!setActiveSuccess)
                        {
                            if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] [SceneManager] Failed to set {currentAdditiveScene.name} as active scene.");
                            continue;
                        }

                        for (int sceneIndex = 0; sceneIndex < currentGroup.scenes.Length; sceneIndex++)
                        {
                            if (currentGroup.scenes[sceneIndex].sceneName == openScene.name)
                            {
                                currentSceneIndex = sceneIndex;
                                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] Found matching scene at index {currentSceneIndex}");

                                await UniTask.Yield(PlayerLoopTiming.Update, linkedCts.Token);
                                OnSceneLoaded(currentAdditiveScene, LoadSceneMode.Additive);

                                return true;
                            }
                        }
                        if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] [SceneManager] Scene {openScene.name} is in Ouroboros group but no matching scene found in currentGroup!");
                    }
                }
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] [SceneManager] No matching Ouroboros scene found in loaded scenes.");
                return false;
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene initialization check cancelled");
                throw;
            }
        }

        private bool IsSceneInOuroborosGroup(string sceneName)
        {
            if (currentGroup == null)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Current group is null when checking scene membership");
                return false;
            }

            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Checking scene membership for {sceneName}");
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Available scenes in group:");
            foreach (var scene in currentGroup.scenes)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}]   - {scene.sceneName}");
            }

            bool isInGroup = currentGroup.scenes.AsValueEnumerable().Any(scene => scene.sceneName == sceneName);
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene {sceneName} is {(isInGroup ? "" : "not ")}in group");
            return isInGroup;
        }

        public string GetCurrentSongSectionName()
        {
            if (currentGroup != null && currentSceneIndex < currentGroup.scenes.Length)
            {
                return currentGroup.scenes[currentSceneIndex].songSections[currentSectionIndex].name;
            }
            return string.Empty;
        }

        public void ChangeSceneWithTransitionToNext()
        {
            // Clear all player locks before changing the scene
            GameManager.Instance.ClearAllPlayerLocks();

            _ = ChangeSceneWithTransitionToNextAsync();
        }

        public async UniTask ChangeSceneWithTransitionToNextAsync(CancellationToken cancellationToken = default)
        {
            await ChangeScene(true, cancellationToken);
        }

        public void ChangeToNextScene()
        {
            _ = ChangeToNextSceneAsync();
        }

        public async UniTask ChangeToNextSceneAsync(CancellationToken cancellationToken = default)
        {
            await ChangeScene(true, cancellationToken);
        }

        public void MoveToNextSectionOrScene()
        {
            // Clear all player locks before moving to the next section or scene
            GameManager.Instance.ClearAllPlayerLocks();

            _ = MoveToNextSectionOrSceneAsync();
        }

        public async UniTask MoveToNextSectionOrSceneAsync(CancellationToken cancellationToken = default)
        {
            await ChangeScene(false, cancellationToken);
        }

        private async UniTask ChangeScene(bool forceNextScene = false, CancellationToken cancellationToken = default)
        {
            float currentTime = Time.time;
            if (currentTime - lastSceneChangeTime < SCENE_CHANGE_COOLDOWN)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene change blocked - cooldown active for {(SCENE_CHANGE_COOLDOWN - (currentTime - lastSceneChangeTime)):F1} more seconds");
                return;
            }

            if (isTransitioning)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene transition already in progress");
                return;
            }

            string nextSceneName = "";
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                isTransitioning = true;
                lastSceneChangeTime = currentTime;

                // Determine next scene index
                int nextSceneIndex = currentSceneIndex;
                if (forceNextScene || currentSectionIndex >= currentGroup.scenes[currentSceneIndex].songSections.Length - 1)
                {
                    nextSceneIndex = (currentSceneIndex + 1) % currentGroup.scenes.Length;
                }

                // If we're staying in the same scene, just move to next section
                if (nextSceneIndex == currentSceneIndex && !forceNextScene)
                {
                    isTransitioning = false;
                    MoveToNextSection();
                    return;
                }

                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Transitioning from scene {currentSceneIndex} to {nextSceneIndex}");

                // Get next scene name
                nextSceneName = currentGroup.scenes[nextSceneIndex].sceneName;

                // Start loading screen fade only if enabled
                if (useLoadingScreen && LoadingScreen.Instance != null)
                {
                    await LoadingScreen.Instance.StartFadeIn(cancellationToken: linkedCts.Token);
                }

                // Reset section index for new scene
                currentSceneIndex = nextSceneIndex;
                currentSectionIndex = 0;
                completedWaves = 0;
                expectedWaves = currentGroup.scenes[currentSceneIndex].songSections[0].waves;
                isFirstUpdate = true;

                // Load the new scene
                await LoadAdditiveSceneAsync(nextSceneName, null, linkedCts.Token);

                // Update music for new scene
                if (AudioManager.Instance != null)
                {
                    float section = currentGroup.scenes[currentSceneIndex].songSections[currentSectionIndex].section;
                    AudioManager.Instance.ApplyMusicChanges(currentGroup, currentSceneIndex, section);
                }

                // Increment spline for new scene
                if (splineManager != null)
                {
                    splineManager.IncrementSpline();
                }

                isTransitioning = false;
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Transition complete to scene {nextSceneName}");
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Scene transition cancelled for {nextSceneName}");
                isTransitioning = false;
                throw;
            }
            catch (System.Exception e)
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error during scene transition: {e.Message}");
                isTransitioning = false;
                throw;
            }
        }

        public string GetCurrentSceneName()
        {
            return SceneManager.GetActiveScene().name;
        }

        public void DebugChangeToNextScene()
        {
            var currentScene = SceneManager.GetActiveScene();
            var scenes = gameObject.scene.buildIndex;
            var nextSceneIndex = (currentScene.buildIndex + 1) % SceneManager.sceneCountInBuildSettings;
            var nextSceneName = System.IO.Path.GetFileNameWithoutExtension(UnityEngine.SceneManagement.SceneUtility.GetScenePathByBuildIndex(nextSceneIndex));

            LoadScene(nextSceneName);
        }

        public bool IsSceneLoaded(string sceneName)
        {
            return SceneManager.GetSceneByName(sceneName).isLoaded;
        }

        public float GetLoadProgress()
        {
            return 0f;
        }

        public bool IsLoading()
        {
            return isTransitioning;
        }

        public void LoadScene(string sceneName)
        {
            if (string.IsNullOrEmpty(sceneName))
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Scene name cannot be null or empty");
                return;
            }

            if (isTransitioning)
            {
                if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] Scene transition already in progress");
                return;
            }

            _ = LoadSceneAsync(sceneName);
        }

        private async UniTask LoadSceneAsync(string sceneName, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(sceneName))
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Scene name cannot be null or empty");
                return;
            }

            if (isTransitioning)
            {
                if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] Scene transition already in progress");
                return;
            }

            var startTime = Time.realtimeSinceStartup;
            var frameTimeStart = Time.unscaledDeltaTime;
            var memoryBefore = GC.GetTotalMemory(false);

            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                sceneEvents?.TriggerSceneLoadStarted(sceneName);

                if (currentAdditiveScene.IsValid() && currentAdditiveScene.isLoaded)
                {
                    if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Unloading current additive scene");
                    await UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(currentAdditiveScene)
                        .ToUniTask(cancellationToken: linkedCts.Token);
                }

                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Loading new scene: {sceneName}");
                var asyncLoad = SceneManager.LoadSceneAsync(sceneName);
                asyncLoad.allowSceneActivation = true;

                float currentProgress = 0f;

                while (!asyncLoad.isDone)
                {
                    linkedCts.Token.ThrowIfCancellationRequested();
                    currentProgress = asyncLoad.progress;

                    var progress = SceneLoadProgress.Create(
                        sceneName,
                        currentProgress,
                        asyncLoad.progress,
                        $"Loading scene assets ({(currentProgress * 100):F0}%)"
                    );

                    gameEvents?.TriggerSceneLoadProgress(currentProgress);
                    LogOperation($"Load progress: {currentProgress:P0}", logCategory);

                    await UniTask.Yield(PlayerLoopTiming.Update, linkedCts.Token);
                }

                currentAdditiveScene = SceneManager.GetSceneByName(sceneName);
                if (!currentAdditiveScene.IsValid())
                {
                    throw new Exception($"Failed to load scene: {sceneName}");
                }

                SceneManager.SetActiveScene(currentAdditiveScene);
                ScoreManager.Instance.CurrentSceneWaveCount = 0;
                OnSceneLoaded(currentAdditiveScene, LoadSceneMode.Additive);

                sceneEvents?.TriggerSceneLoadCompleted(sceneName);

                // Record performance metrics
                var loadTime = Time.realtimeSinceStartup - startTime;
                var memoryAfter = GC.GetTotalMemory(false);
                var frameTimeImpact = Time.unscaledDeltaTime - frameTimeStart;

                _performanceMetrics[sceneName] = new ScenePerformanceMetrics
                {
                    LoadTime = loadTime,
                    TransitionTime = loadTime, // In this case, they're the same
                    MemoryUsageBefore = (int)(memoryBefore / 1024 / 1024), // Convert to MB
                    MemoryUsageAfter = (int)(memoryAfter / 1024 / 1024),
                    FrameTimeImpact = frameTimeImpact
                };

                LogOperation($"Scene {sceneName} loaded in {loadTime:F2}s with {((memoryAfter - memoryBefore) / 1024 / 1024):F2}MB memory impact", logCategory);
            }
            catch (OperationCanceledException)
            {
                LogOperation($"Scene load cancelled for {sceneName}", logCategory);
                if (enableDebugLogs) Debug.LogWarning($"[{GetType().Name}] Scene load cancelled for {sceneName}");
                throw;
            }
            catch (Exception e)
            {
                LogOperation($"Error loading scene {sceneName}: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error loading scene: {e.Message}");
                throw;
            }
            finally
            {
                isTransitioning = false;
            }
        }

        private void LogOperation(string operation)
        {
            _operationLog.Enqueue($"[{DateTime.Now:HH:mm:ss.fff}] {operation}");
            while (_operationLog.Count > MAX_LOG_ENTRIES)
            {
                _operationLog.Dequeue();
            }
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] {operation}");
        }

        private void LogOperation(string operation, string category)
        {
            _operationLog.Enqueue($"[{DateTime.Now:HH:mm:ss.fff}] {operation}");
            while (_operationLog.Count > MAX_LOG_ENTRIES)
            {
                _operationLog.Dequeue();
            }
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] {operation}");
        }

        /// <summary>
        /// Returns the current performance metrics for all scene operations.
        /// Useful for monitoring and debugging scene loading performance.
        /// </summary>
        /// <returns>Dictionary of scene names to their performance metrics</returns>
        public IReadOnlyDictionary<string, ScenePerformanceMetrics> GetPerformanceMetrics()
        {
            return _performanceMetrics;
        }

        /// <summary>
        /// Returns recent operation log entries for debugging.
        /// Includes timestamps and detailed operation descriptions.
        /// </summary>
        /// <returns>Array of recent operation log entries</returns>
        public string[] GetRecentOperations()
        {
            return _operationLog.AsValueEnumerable().ToArray();
        }

        public void HandleSceneLoadStarted(string sceneName)
        {
            if (isHandlingSceneLoad) return;

            try
            {
                isHandlingSceneLoad = true;
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Starting to load scene: {sceneName}");
                sceneEvents?.TriggerSceneLoadStarted(sceneName);
            }
            finally
            {
                isHandlingSceneLoad = false;
            }
        }

        public void HandleSceneLoadCompleted(string sceneName)
        {
            if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Completed loading scene: {sceneName}");
        }

        public async UniTask LoadBaseSceneAsync(CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(baseSceneName))
            {
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Base scene name is not set in the inspector.");
                return;
            }

            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                baseScene = SceneManager.GetSceneByName(baseSceneName);
                if (!baseScene.isLoaded)
                {
                    await SceneManager.LoadSceneAsync(baseSceneName, LoadSceneMode.Single)
                        .ToUniTask(cancellationToken: linkedCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                if (enableDebugLogs) Debug.Log($"[{GetType().Name}] Base scene load cancelled");
                throw;
            }
        }

        #region Wave System Async Implementation
        private async UniTask HandleWaveStartedAsync(int waveNumber, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                LogOperation($"[WAVE] Starting wave {waveNumber}", logCategory);
                currentWaveCount++;

                if (isFirstUpdate)
                {
                    await InitializeFirstWaveAsync(linkedCts.Token);
                }
                else if (CurrentSection.waves == 0 && currentSectionIndex < CurrentScene.songSections.Length - 1)
                {
                    LogOperation($"[WAVE] Wave started in transition section {CurrentSection.name}, moving to next section", logCategory);
                    await MoveToNextSectionAsync(linkedCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                LogOperation($"[WAVE] Wave start cancelled for wave {waveNumber}", logCategory);
                throw;
            }
            catch (Exception e)
            {
                LogOperation($"[WAVE] Error in HandleWaveStartedAsync: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error handling wave start: {e.Message}");
                throw;
            }
        }

        private async UniTask InitializeFirstWaveAsync(CancellationToken cancellationToken)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                isFirstUpdate = false;
                expectedWaves = CurrentSection.waves;

                LogOperation($"[WAVE] Initializing first wave in section {CurrentSection.name} (Expected waves: {expectedWaves})", logCategory);

                await UpdateMusicSectionAsync(linkedCts.Token);
                waveEventChannel.TriggerSectionStarted(CurrentSection.name, expectedWaves);

                if (expectedWaves == 0 && currentSectionIndex < CurrentScene.songSections.Length - 1)
                {
                    LogOperation("[WAVE] Starting in 0-wave section, moving to next section", logCategory);
                    await MoveToNextSectionAsync(linkedCts.Token);
                }
            }
            catch (Exception e)
            {
                LogOperation($"[WAVE] Error in InitializeFirstWaveAsync: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error initializing first wave: {e.Message}");
                throw;
            }
        }

        private async UniTask HandleWaveCompletedAsync(int waveNumber, CancellationToken cancellationToken = default)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                if (expectedWaves > 0)
                {
                    completedWaves++;
                    LogOperation($"[WAVE] Wave {waveNumber} completed ({completedWaves}/{expectedWaves})", logCategory);

                    if (completedWaves >= expectedWaves)
                    {
                        await HandleSectionCompletionAsync(linkedCts.Token);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogOperation($"[WAVE] Wave completion cancelled for wave {waveNumber}", logCategory);
                throw;
            }
            catch (Exception e)
            {
                LogOperation($"[WAVE] Error in HandleWaveCompletedAsync: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error handling wave completion: {e.Message}");
                throw;
            }
        }

        private async UniTask HandleSectionCompletionAsync(CancellationToken cancellationToken)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                LogOperation($"[WAVE] Section {GetCurrentSongSectionName()} completed", logCategory);
                waveEventChannel.TriggerSectionCompleted();

                await MoveToNextSectionOrSceneAsync(linkedCts.Token);
            }
            catch (Exception e)
            {
                LogOperation($"[WAVE] Error in HandleSectionCompletionAsync: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error handling section completion: {e.Message}");
                throw;
            }
        }

        private async UniTask MoveToNextSectionAsync(CancellationToken cancellationToken)
        {
            try
            {
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

                var oldSection = CurrentSection;

                currentSectionIndex++;
                completedWaves = 0;

                if (currentSectionIndex >= CurrentScene.songSections.Length)
                {
                    currentSectionIndex = CurrentScene.songSections.Length - 1;
                    LogOperation("[SCENE] Reached end of sections, staying on last section", logCategory);
                    return;
                }

                expectedWaves = CurrentSection.waves;

                LogOperation($"[SCENE] Moving from section {oldSection.name} to {CurrentSection.name}", logCategory);

                if (Math.Abs(oldSection.section - CurrentSection.section) > 0.01f)
                {
                    LogOperation($"[SCENE] Section value changed, updating music", logCategory);
                    await UpdateMusicSectionAsync(linkedCts.Token);
                }

                waveEventChannel.TriggerSectionStarted(CurrentSection.name, expectedWaves);

                if (splineManager != null)
                {
                    LogOperation($"[SCENE] Incrementing spline for section {CurrentSection.name}", logCategory);
                    splineManager.IncrementSpline();
                }

                if (expectedWaves == 0)
                {
                    LogOperation($"[SCENE] In transition section {CurrentSection.name}, waiting for next wave start", logCategory);
                }
            }
            catch (Exception e)
            {
                LogOperation($"[SCENE] Error moving to next section: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error in section transition: {e.Message}");
                throw;
            }
        }

        private async UniTask UpdateMusicSectionAsync(CancellationToken cancellationToken)
        {
            try
            {
                await UniTask.SwitchToMainThread(cancellationToken);

                if (AudioManager.Instance == null)
                {
                    LogOperation("[SceneManager] AudioManager.Instance is null!", logCategory);
                    return;
                }

                float section = CurrentSection.section;

                LogOperation($"[SCENE] Updating music section to {section} for scene {CurrentScene.sceneName}", logCategory);

                await AudioManager.Instance.ChangeSongSectionAsync(currentGroup, currentSceneIndex, section, cancellationToken);
                await AudioManager.Instance.ApplyMusicChangesAsync(currentGroup, currentSceneIndex, section, cancellationToken);

                currentSongSectionDisplay = $"Scene {currentSceneIndex} - {CurrentSection.name} (Value: {section})";
            }
            catch (Exception e)
            {
                LogOperation($"[SCENE] Error updating music section: {e.Message}", logCategory);
                if (enableDebugLogs) Debug.LogError($"[{GetType().Name}] Error updating music: {e.Message}");
                throw;
            }
        }
        #endregion

        // Helper properties for cleaner code
        private SceneInfo CurrentScene => currentGroup.scenes[currentSceneIndex];
        private SongSection CurrentSection => CurrentScene.songSections[currentSectionIndex];
    }
}
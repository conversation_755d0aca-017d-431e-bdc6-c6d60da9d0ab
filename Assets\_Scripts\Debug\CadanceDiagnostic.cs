using UnityEngine;
using System.Collections;
using Stylo.Cadance;
using BTR;

/// <summary>
/// Comprehensive diagnostic tool for the complete shooting system.
/// Monitors Cadance, input detection, lock mechanics, and shooting conditions.
/// </summary>
public class CadanceDiagnostic : MonoBehaviour
{
    [Header("Diagnostic Settings")]
    [SerializeField] private bool enableDiagnostics = true;
    [SerializeField] private bool enableVerboseLogging = true;
    [SerializeField] private float diagnosticInterval = 0.5f; // More frequent checks
    [SerializeField] private bool enableInputMonitoring = true;
    
    [Header("Event Testing")]
    [SerializeField] private string testEventID = ""; // Copy from CrosshairCore.eventIDShooting
    [SerializeField] private bool testEventRegistration = true;
    
    [Header("Cadance Status")]
    [SerializeField] private bool cadanceInstanceExists = false;
    [SerializeField] private bool cadanceIsPlaying = false;
    [SerializeField] private bool eventsRegistered = false;
    [SerializeField] private int totalEventsReceived = 0;
    [SerializeField] private float lastEventTime = 0f;
    
    [Header("Input Status")]
    [SerializeField] private bool lockButtonPressed = false;
    [SerializeField] private bool lockButtonJustReleased = false;
    [SerializeField] private float lastButtonReleaseTime = 0f;
    
    [Header("Locking Status")]
    [SerializeField] private int lockedProjectileCount = 0;
    [SerializeField] private int lockedEnemyCount = 0;
    [SerializeField] private bool triggeredLockFire = false;
    
    [Header("Shooting Conditions")]
    [SerializeField] private bool directShootingMode = false;
    [SerializeField] private bool bypassMusicalTiming = false;
    [SerializeField] private float currentTimeScale = 1f;
    [SerializeField] private bool allConditionsMet = false;
    
    [Header("System References")]
    private Cadance cadanceInstance;
    private CrosshairCore crosshairCore;
    private PlayerLocking playerLocking;
    private PlayerShooting playerShooting;
    private Coroutine diagnosticCoroutine;
    
    // Input tracking
    private bool wasLockPressed = false;
    private int shootingEventsDetected = 0;
    private float lastShootingEventTime = 0f;
    
    private void Start()
    {
        if (enableDiagnostics)
        {
            StartDiagnostics();
        }
    }
    
    private void StartDiagnostics()
    {
        Debug.Log("=== COMPREHENSIVE SHOOTING SYSTEM DIAGNOSTIC STARTED ===");
        
        // Find all necessary components
        crosshairCore = FindFirstObjectByType<CrosshairCore>();
        if (crosshairCore != null)
        {
            Debug.Log("[CadanceDiagnostic] ✅ CrosshairCore found");
            
            // Get PlayerLocking and PlayerShooting from CrosshairCore
            playerLocking = crosshairCore.GetComponent<PlayerLocking>();
            playerShooting = crosshairCore.GetComponent<PlayerShooting>();
            
            if (playerLocking != null)
                Debug.Log("[CadanceDiagnostic] ✅ PlayerLocking found");
            else
                Debug.LogError("[CadanceDiagnostic] ❌ PlayerLocking NOT found!");
                
            if (playerShooting != null)
                Debug.Log("[CadanceDiagnostic] ✅ PlayerShooting found");
            else
                Debug.LogError("[CadanceDiagnostic] ❌ PlayerShooting NOT found!");
            
            // Use reflection to get the eventIDShooting field
            var field = typeof(CrosshairCore).GetField("eventIDShooting", 
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                testEventID = (string)field.GetValue(crosshairCore);
                Debug.Log($"[CadanceDiagnostic] Found shooting event ID: '{testEventID}'");
            }
            else
            {
                Debug.LogError("[CadanceDiagnostic] ❌ Could not find eventIDShooting field!");
            }
        }
        else
        {
            Debug.LogError("[CadanceDiagnostic] ❌ CrosshairCore NOT found! Cannot proceed with diagnostics.");
        }
        
        diagnosticCoroutine = StartCoroutine(RunDiagnostics());
    }
    
    private IEnumerator RunDiagnostics()
    {
        while (enableDiagnostics)
        {
            PerformDiagnosticCheck();
            yield return new WaitForSeconds(diagnosticInterval);
        }
    }
    
    private void PerformDiagnosticCheck()
    {
        if (enableVerboseLogging)
            Debug.Log("--- COMPREHENSIVE SHOOTING SYSTEM CHECK ---");
        
        // === CADANCE SYSTEM CHECK ===
        CheckCadanceSystem();
        
        // === INPUT SYSTEM CHECK ===
        if (enableInputMonitoring)
            CheckInputSystem();
        
        // === LOCKING SYSTEM CHECK ===
        CheckLockingSystem();
        
        // === SHOOTING CONDITIONS CHECK ===
        CheckShootingConditions();
        
        // === OVERALL SYSTEM STATUS ===
        AnalyzeOverallStatus();
    }
    
    private void CheckCadanceSystem()
    {
        // Check 1: Cadance Instance
        cadanceInstance = Cadance.Instance;
        cadanceInstanceExists = cadanceInstance != null;
        
        if (!cadanceInstanceExists)
        {
            Debug.LogError("[CadanceDiagnostic] ❌ CADANCE INSTANCE IS NULL! This is the root cause of shooting issues.");
            return;
        }
        
        // Check 2: Cadance Playing State
        try
        {
            var playingProperty = typeof(Cadance).GetProperty("IsPlaying");
            if (playingProperty != null)
            {
                cadanceIsPlaying = (bool)playingProperty.GetValue(cadanceInstance);
            }
            else
            {
                cadanceIsPlaying = true; // Assume playing if property doesn't exist
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[CadanceDiagnostic] Could not check Cadance playing state: {e.Message}");
            cadanceIsPlaying = true;
        }
        
        // Check 3: Event Registration Test
        if (testEventRegistration && !string.IsNullOrEmpty(testEventID))
        {
            TestEventRegistration();
        }
        
        if (enableVerboseLogging)
        {
            Debug.Log($"[CadanceDiagnostic] Cadance Status - Instance: {cadanceInstanceExists}, Playing: {cadanceIsPlaying}, Events: {totalEventsReceived}");
        }
    }
    
    private void CheckInputSystem()
    {
        if (crosshairCore == null) return;
        
        // Monitor lock button state
        bool currentLockPressed = crosshairCore.CheckLockProjectiles();
        lockButtonPressed = currentLockPressed;
        
        // Detect button release
        if (wasLockPressed && !currentLockPressed)
        {
            lockButtonJustReleased = true;
            lastButtonReleaseTime = Time.time;
            Debug.Log($"[CadanceDiagnostic] 🔄 LOCK BUTTON RELEASED at {Time.time:F2}!");
        }
        else
        {
            lockButtonJustReleased = false;
        }
        
        wasLockPressed = currentLockPressed;
        
        if (enableVerboseLogging && currentLockPressed)
        {
            Debug.Log($"[CadanceDiagnostic] Lock button currently HELD");
        }
    }
    
    private void CheckLockingSystem()
    {
        if (playerLocking == null) return;
        
        // Update locking status
        lockedProjectileCount = playerLocking.GetLockedProjectileCount();
        triggeredLockFire = playerLocking.triggeredLockFire;
        
        // Get locked enemies count
        var lockedEnemies = playerLocking.GetLockedEnemies();
        lockedEnemyCount = lockedEnemies != null ? lockedEnemies.Count : 0;
        
        if (enableVerboseLogging)
        {
            Debug.Log($"[CadanceDiagnostic] Locking Status - Projectiles: {lockedProjectileCount}, Enemies: {lockedEnemyCount}, TriggeredFire: {triggeredLockFire}");
        }
        
        // Alert when conditions are met
        if (lockedProjectileCount > 0 && triggeredLockFire)
        {
            Debug.Log($"[CadanceDiagnostic] 🎯 SHOOTING CONDITIONS MET! Locked: {lockedProjectileCount}, Triggered: {triggeredLockFire}");
        }
    }
    
    private void CheckShootingConditions()
    {
        if (crosshairCore == null) return;
        
        // Get shooting mode settings
        var directModeField = typeof(CrosshairCore).GetField("enableDirectShootingMode", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var bypassField = typeof(CrosshairCore).GetField("bypassMusicalTiming", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
        if (directModeField != null)
            directShootingMode = (bool)directModeField.GetValue(crosshairCore);
        if (bypassField != null)
            bypassMusicalTiming = (bool)bypassField.GetValue(crosshairCore);
            
        currentTimeScale = Time.timeScale;
        
        // Calculate if all conditions are met for shooting
        bool lockPressed = crosshairCore.CheckLockProjectiles();
        allConditionsMet = (!lockPressed || triggeredLockFire) && lockedProjectileCount > 0 && currentTimeScale != 0f;
        
        if (enableVerboseLogging)
        {
            Debug.Log($"[CadanceDiagnostic] Shooting Conditions - Direct: {directShootingMode}, Bypass: {bypassMusicalTiming}, TimeScale: {currentTimeScale}, AllMet: {allConditionsMet}");
        }
        
        // Alert about shooting modes
        if (directShootingMode || bypassMusicalTiming)
        {
            Debug.Log($"[CadanceDiagnostic] ⚠️ DIRECT SHOOTING MODE ENABLED - Bypassing musical timing!");
        }
    }
    
    private void AnalyzeOverallStatus()
    {
        if (enableVerboseLogging)
        {
            Debug.Log("[CadanceDiagnostic] === SYSTEM STATUS SUMMARY ===");
            Debug.Log($"[CadanceDiagnostic] Cadance Working: {cadanceInstanceExists && cadanceIsPlaying}");
            Debug.Log($"[CadanceDiagnostic] Input Working: {crosshairCore != null}");
            Debug.Log($"[CadanceDiagnostic] Locking Working: {playerLocking != null}");
            Debug.Log($"[CadanceDiagnostic] Shooting Ready: {allConditionsMet}");
            Debug.Log($"[CadanceDiagnostic] Events Received: {totalEventsReceived}");
        }
        
        // Provide specific guidance
        if (!cadanceInstanceExists)
        {
            Debug.LogError("[CadanceDiagnostic] 🔴 PRIMARY ISSUE: Cadance system not working - Enable direct shooting mode!");
        }
        else if (totalEventsReceived == 0 && Time.time > 5f)
        {
            Debug.LogWarning("[CadanceDiagnostic] 🟡 Cadance exists but no events received - Check event ID configuration!");
        }
        else if (cadanceInstanceExists && totalEventsReceived > 0)
        {
            Debug.Log("[CadanceDiagnostic] 🟢 Cadance system working correctly!");
        }
    }
    
    private void TestEventRegistration()
    {
        if (!eventsRegistered)
        {
            try
            {
                cadanceInstance.RegisterForEvents(testEventID, OnTestEvent);
                eventsRegistered = true;
                Debug.Log($"✓ Successfully registered for event: '{testEventID}'");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Failed to register for event '{testEventID}': {e.Message}");
            }
        }
    }
    
    private void OnTestEvent(CadanceEvent evt)
    {
        totalEventsReceived++;
        lastEventTime = Time.time;
        
        Debug.Log($"🎵 CADANCE EVENT RECEIVED! Event: '{testEventID}', Total: {totalEventsReceived}, Time: {Time.time:F2}");
        
        // This proves Cadance is working and firing events
        if (totalEventsReceived == 1)
        {
            Debug.Log("✅ CADANCE IS WORKING! Events are firing correctly.");
            Debug.Log("🔍 If shooting still doesn't work, check CrosshairCore.OnMusicalShoot() conditions");
        }
    }
    
    [ContextMenu("Force Diagnostic Check")]
    public void ForceDiagnosticCheck()
    {
        PerformDiagnosticCheck();
    }
    
    [ContextMenu("Test Manual Event")]
    public void TestManualEvent()
    {
        if (cadanceInstance != null && !string.IsNullOrEmpty(testEventID))
        {
            Debug.Log($"🧪 Testing manual event trigger for: '{testEventID}'");
            // Note: This would require access to Cadance's internal event system
            // For now, just log that we're attempting it
        }
    }
    
    private void OnDestroy()
    {
        if (diagnosticCoroutine != null)
        {
            StopCoroutine(diagnosticCoroutine);
        }
        
        // Note: Cadance doesn't provide a direct unregister method
        // Events will be automatically cleaned up when the object is destroyed
    }
    
    private void OnGUI()
    {
        if (!enableDiagnostics) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 500, 600));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("🔍 COMPREHENSIVE SHOOTING SYSTEM DIAGNOSTIC", GUI.skin.label);
        
        // === CADANCE SYSTEM ===
        GUILayout.Space(5);
        GUILayout.Label("🎵 CADANCE SYSTEM", GUI.skin.label);
        GUILayout.Label($"Instance: {(cadanceInstanceExists ? "✅ EXISTS" : "❌ NULL")}");
        GUILayout.Label($"Playing: {(cadanceIsPlaying ? "✅ YES" : "❌ NO")}");
        GUILayout.Label($"Events Registered: {(eventsRegistered ? "✅ YES" : "❌ NO")}");
        GUILayout.Label($"Event ID: '{testEventID}'");
        GUILayout.Label($"Total Events Received: {totalEventsReceived}");
        
        if (lastEventTime > 0)
        {
            float timeSince = Time.time - lastEventTime;
            GUILayout.Label($"Last Event: {timeSince:F1}s ago");
        }
        
        // === INPUT SYSTEM ===
        GUILayout.Space(10);
        GUILayout.Label("🎮 INPUT SYSTEM", GUI.skin.label);
        GUILayout.Label($"Lock Button Pressed: {(lockButtonPressed ? "✅ HELD" : "❌ RELEASED")}");
        GUILayout.Label($"Just Released: {(lockButtonJustReleased ? "✅ YES" : "❌ NO")}");
        
        if (lastButtonReleaseTime > 0)
        {
            float timeSince = Time.time - lastButtonReleaseTime;
            GUILayout.Label($"Last Release: {timeSince:F1}s ago");
        }
        
        // === LOCKING SYSTEM ===
        GUILayout.Space(10);
        GUILayout.Label("🎯 LOCKING SYSTEM", GUI.skin.label);
        GUILayout.Label($"Locked Projectiles: {lockedProjectileCount}");
        GUILayout.Label($"Locked Enemies: {lockedEnemyCount}");
        GUILayout.Label($"Triggered Lock Fire: {(triggeredLockFire ? "✅ YES" : "❌ NO")}");
        
        // === SHOOTING CONDITIONS ===
        GUILayout.Space(10);
        GUILayout.Label("🚀 SHOOTING CONDITIONS", GUI.skin.label);
        GUILayout.Label($"Direct Shooting Mode: {(directShootingMode ? "✅ ENABLED" : "❌ DISABLED")}");
        GUILayout.Label($"Bypass Musical Timing: {(bypassMusicalTiming ? "✅ ENABLED" : "❌ DISABLED")}");
        GUILayout.Label($"Time Scale: {currentTimeScale:F2}");
        GUILayout.Label($"All Conditions Met: {(allConditionsMet ? "✅ YES" : "❌ NO")}");
        
        // === OVERALL STATUS ===
        GUILayout.Space(10);
        GUILayout.Label("📊 OVERALL STATUS", GUI.skin.label);
        
        string overallStatus;
        if (!cadanceInstanceExists)
        {
            overallStatus = "🔴 BROKEN - Cadance system missing";
        }
        else if (totalEventsReceived == 0 && Time.time > 5f)
        {
            overallStatus = "🟡 ISSUE - No events received";
        }
        else if (cadanceInstanceExists && totalEventsReceived > 0)
        {
            overallStatus = "🟢 WORKING - System operational";
        }
        else
        {
            overallStatus = "🟡 STARTING - System initializing";
        }
        
        GUILayout.Label($"System Status: {overallStatus}");
        
        // === ACTION BUTTONS ===
        GUILayout.Space(15);
        
        if (!cadanceInstanceExists || (totalEventsReceived == 0 && Time.time > 5f))
        {
            if (GUILayout.Button("🔧 ENABLE DIRECT SHOOTING MODE (FIX)", GUILayout.Height(30)))
            {
                EnableDirectShootingMode();
            }
        }
        
        if (GUILayout.Button("📋 PRINT DETAILED REPORT", GUILayout.Height(25)))
        {
            PrintDetailedReport();
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
    
    private void EnableDirectShootingMode()
    {
        if (crosshairCore == null)
        {
            Debug.LogError("[CadanceDiagnostic] Cannot enable direct shooting - CrosshairCore not found!");
            return;
        }
        
        // Use reflection to enable direct shooting mode
        var directModeField = typeof(CrosshairCore).GetField("enableDirectShootingMode", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var bypassField = typeof(CrosshairCore).GetField("bypassMusicalTiming", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
        if (directModeField != null)
        {
            directModeField.SetValue(crosshairCore, true);
            Debug.Log("[CadanceDiagnostic] ✅ enableDirectShootingMode set to TRUE");
        }
        
        if (bypassField != null)
        {
            bypassField.SetValue(crosshairCore, true);
            Debug.Log("[CadanceDiagnostic] ✅ bypassMusicalTiming set to TRUE");
        }
        
        Debug.Log("[CadanceDiagnostic] 🚀 DIRECT SHOOTING MODE ENABLED! Shooting should now work immediately on button release.");
    }
    
    private void PrintDetailedReport()
    {
        Debug.Log("=== COMPREHENSIVE SHOOTING SYSTEM DIAGNOSTIC REPORT ===");
        Debug.Log($"Time: {System.DateTime.Now}");
        Debug.Log($"Game Time: {Time.time:F2}s");
        
        Debug.Log("\n--- CADANCE SYSTEM ---");
        Debug.Log($"Instance Exists: {cadanceInstanceExists}");
        Debug.Log($"Is Playing: {cadanceIsPlaying}");
        Debug.Log($"Events Registered: {eventsRegistered}");
        Debug.Log($"Event ID: '{testEventID}'");
        Debug.Log($"Total Events Received: {totalEventsReceived}");
        Debug.Log($"Last Event Time: {lastEventTime:F2}s");
        
        Debug.Log("\n--- INPUT SYSTEM ---");
        Debug.Log($"Lock Button Pressed: {lockButtonPressed}");
        Debug.Log($"Just Released: {lockButtonJustReleased}");
        Debug.Log($"Last Release Time: {lastButtonReleaseTime:F2}s");
        
        Debug.Log("\n--- LOCKING SYSTEM ---");
        Debug.Log($"Locked Projectiles: {lockedProjectileCount}");
        Debug.Log($"Locked Enemies: {lockedEnemyCount}");
        Debug.Log($"Triggered Lock Fire: {triggeredLockFire}");
        
        Debug.Log("\n--- SHOOTING CONDITIONS ---");
        Debug.Log($"Direct Shooting Mode: {directShootingMode}");
        Debug.Log($"Bypass Musical Timing: {bypassMusicalTiming}");
        Debug.Log($"Time Scale: {currentTimeScale:F2}");
        Debug.Log($"All Conditions Met: {allConditionsMet}");
        
        Debug.Log("\n--- RECOMMENDATIONS ---");
        if (!cadanceInstanceExists)
        {
            Debug.Log("❌ PRIMARY ISSUE: Cadance system is not working. Enable direct shooting mode.");
        }
        else if (totalEventsReceived == 0 && Time.time > 5f)
        {
            Debug.Log("⚠️ Cadance exists but no events received. Check event ID configuration.");
        }
        else if (allConditionsMet && totalEventsReceived > 0)
        {
            Debug.Log("✅ System appears to be working correctly.");
        }
        
        Debug.Log("=== END DIAGNOSTIC REPORT ===");
    }
}

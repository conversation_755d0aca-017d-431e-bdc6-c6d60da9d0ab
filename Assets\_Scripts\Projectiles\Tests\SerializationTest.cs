using UnityEngine;

namespace BTR.Projectiles.Tests
{
    /// <summary>
    /// Simple test to verify that the serialization conflict is resolved.
    /// This script can be added to a GameObject to test the manager hierarchy.
    /// </summary>
    public class SerializationTest : MonoBehaviour
    {
        [Header("Serialization Test")]
        [Tooltip("Enable to test manager serialization")]
        [SerializeField] private bool testSerialization = true;
        
        private void Start()
        {
            if (testSerialization)
            {
                TestManagerSerialization();
            }
        }
        
        [ContextMenu("Test Manager Serialization")]
        public void TestManagerSerialization()
        {
            Debug.Log("=== Testing Manager Serialization ===");
            
            // Test ProjectileManager serialization
            if (ProjectileManager.HasInstance)
            {
                var manager = ProjectileManager.Instance;
                Debug.Log($"✅ ProjectileManager found: {manager.ManagerId}");
                Debug.Log($"   IsInitialized: {manager.IsInitialized}");
                Debug.Log($"   IsActive: {manager.IsActive}");
                Debug.Log("✅ No serialization conflicts detected");
            }
            else
            {
                Debug.LogWarning("⚠️ ProjectileManager not found in scene");
            }
            
            Debug.Log("=== Serialization Test Complete ===");
        }
    }
}

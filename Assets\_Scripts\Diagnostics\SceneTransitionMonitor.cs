using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;

namespace BTR
{
    /// <summary>
    /// Runtime monitoring tool for scene transitions with real-time status display.
    /// Provides immediate feedback on transition state and potential issues.
    /// </summary>
    public class SceneTransitionMonitor : MonoBehaviour
    {
        [Header("Monitor Settings")]
        [SerializeField] private bool enableMonitoring = true;
        [SerializeField] private bool showOnScreenDisplay = true;
        [SerializeField] private bool autoDetectStuckTransitions = true;
        [SerializeField] private float stuckTransitionTimeout = 30f;
        [SerializeField] private bool trackCancellationTokens = true;

        [Header("Display Settings")]
        [SerializeField] private KeyCode toggleDisplayKey = KeyCode.F12;
        [SerializeField] private Vector2 displayPosition = new Vector2(10, 10);
        [SerializeField] private int maxDisplayLines = 20;

        // Monitoring State
        private SceneTransitionDiagnostics diagnostics;
        private readonly Dictionary<string, float> transitionStartTimes = new();
        private readonly List<string> recentEvents = new();
        private readonly Dictionary<string, int> eventCounts = new();
        private bool displayVisible = true;
        private float lastEventTime;
        private string lastEventMessage;

        // Current State Tracking
        private bool isCurrentlyTransitioning;
        private string currentTransitionTarget;
        private float currentTransitionStartTime;
        private int activeAsyncOperations;
        private List<string> activeScenes = new();
        private string lastFailureReason;
        private float lastFailureTime;

        // Performance Tracking
        private float averageTransitionTime;
        private int completedTransitions;
        private float longestTransition;
        private float shortestTransition = float.MaxValue;

        private void Awake()
        {
            if (!enableMonitoring) return;
            
            diagnostics = FindObjectOfType<SceneTransitionDiagnostics>();
            if (diagnostics == null)
            {
                Debug.LogWarning("[SceneTransitionMonitor] SceneTransitionDiagnostics not found. Some monitoring features may be limited.");
            }
        }

        private void Start()
        {
            if (!enableMonitoring) return;
            
            SubscribeToEvents();
            UpdateActiveScenes();
            LogEvent("Monitor", "Scene Transition Monitor started");
        }

        private void Update()
        {
            if (!enableMonitoring) return;
            
            if (Input.GetKeyDown(toggleDisplayKey))
            {
                displayVisible = !displayVisible;
            }

            CheckForStuckTransitions();
            MonitorAsyncOperations();
        }

        private void SubscribeToEvents()
        {
            // Unity Scene Manager Events
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;
            SceneManager.activeSceneChanged += OnActiveSceneChanged;

            // Monitor SceneManagerBTR if available
            var sceneManagerBTR = FindObjectOfType<SceneManagerBTR>();
            if (sceneManagerBTR != null)
            {
                LogEvent("Monitor", "Found SceneManagerBTR - enhanced monitoring enabled");
            }
        }

        private void UpdateActiveScenes()
        {
            activeScenes.Clear();
            for (int i = 0; i < SceneManager.sceneCount; i++)
            {
                var scene = SceneManager.GetSceneAt(i);
                if (scene.isLoaded)
                {
                    activeScenes.Add(scene.name);
                }
            }
        }

        private void CheckForStuckTransitions()
        {
            if (!autoDetectStuckTransitions || !isCurrentlyTransitioning) return;

            float transitionDuration = Time.time - currentTransitionStartTime;
            if (transitionDuration > stuckTransitionTimeout)
            {
                string errorMsg = $"Transition to '{currentTransitionTarget}' appears stuck after {transitionDuration:F1}s";
                LogEvent("Error", errorMsg);
                Debug.LogError($"[SceneTransitionMonitor] {errorMsg}");
                
                lastFailureReason = "Timeout - transition stuck";
                lastFailureTime = Time.time;
                
                // Report to diagnostics if available
                diagnostics?.ReportTransitionFailure(currentTransitionTarget, lastFailureReason);
                
                // Reset monitoring state
                isCurrentlyTransitioning = false;
                currentTransitionTarget = null;
            }
        }

        private void MonitorAsyncOperations()
        {
            // Count active async operations (this is a simplified check)
            int currentAsyncOps = 0;
            
            // Check if any scenes are currently loading
            for (int i = 0; i < SceneManager.sceneCount; i++)
            {
                var scene = SceneManager.GetSceneAt(i);
                if (!scene.isLoaded)
                {
                    currentAsyncOps++;
                }
            }

            if (currentAsyncOps != activeAsyncOperations)
            {
                LogEvent("AsyncOps", $"Active async operations: {activeAsyncOperations} -> {currentAsyncOps}");
                activeAsyncOperations = currentAsyncOps;
            }
        }

        private void LogEvent(string category, string message)
        {
            lastEventTime = Time.time;
            lastEventMessage = $"[{category}] {message}";
            
            recentEvents.Add($"{Time.time:F2}: {lastEventMessage}");
            
            // Maintain max display lines
            while (recentEvents.Count > maxDisplayLines)
            {
                recentEvents.RemoveAt(0);
            }

            // Count events by category
            if (!eventCounts.ContainsKey(category))
            {
                eventCounts[category] = 0;
            }
            eventCounts[category]++;

            // Log to diagnostics if available
            diagnostics?.LogCustomEvent(category, message);
        }

        // Event Handlers
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            LogEvent("SceneLoaded", $"{scene.name} ({mode})");
            UpdateActiveScenes();
            
            // Check if this completes a transition we're monitoring
            if (isCurrentlyTransitioning && scene.name == currentTransitionTarget)
            {
                float transitionTime = Time.time - currentTransitionStartTime;
                LogEvent("TransitionComplete", $"'{scene.name}' loaded in {transitionTime:F2}s");
                
                // Update performance stats
                completedTransitions++;
                averageTransitionTime = ((averageTransitionTime * (completedTransitions - 1)) + transitionTime) / completedTransitions;
                longestTransition = Mathf.Max(longestTransition, transitionTime);
                shortestTransition = Mathf.Min(shortestTransition, transitionTime);
                
                isCurrentlyTransitioning = false;
                currentTransitionTarget = null;
            }
        }

        private void OnSceneUnloaded(Scene scene)
        {
            LogEvent("SceneUnloaded", scene.name);
            UpdateActiveScenes();
        }

        private void OnActiveSceneChanged(Scene current, Scene next)
        {
            LogEvent("ActiveSceneChanged", $"{current.name} -> {next.name}");
        }

        // Public API for external systems to report transition events
        public void ReportTransitionStarted(string sceneName)
        {
            if (!enableMonitoring) return;
            
            LogEvent("TransitionStarted", sceneName);
            isCurrentlyTransitioning = true;
            currentTransitionTarget = sceneName;
            currentTransitionStartTime = Time.time;
            transitionStartTimes[sceneName] = Time.time;
        }

        public void ReportTransitionFailed(string sceneName, string reason)
        {
            if (!enableMonitoring) return;
            
            LogEvent("TransitionFailed", $"{sceneName}: {reason}");
            lastFailureReason = reason;
            lastFailureTime = Time.time;
            
            if (sceneName == currentTransitionTarget)
            {
                isCurrentlyTransitioning = false;
                currentTransitionTarget = null;
            }
        }

        public void ReportAsyncOperationStarted(string operationType)
        {
            if (!enableMonitoring) return;
            LogEvent("AsyncOpStarted", operationType);
        }

        public void ReportAsyncOperationCompleted(string operationType, bool success)
        {
            if (!enableMonitoring) return;
            LogEvent("AsyncOpCompleted", $"{operationType} - {(success ? "Success" : "Failed")}");
        }

        // On-screen display
        private void OnGUI()
        {
            if (!enableMonitoring || !showOnScreenDisplay || !displayVisible) return;

            GUILayout.BeginArea(new Rect(displayPosition.x, displayPosition.y, 600, 400));
            GUILayout.BeginVertical("box");

            // Header
            GUILayout.Label("Scene Transition Monitor", GUI.skin.box);
            GUILayout.Label($"Press {toggleDisplayKey} to toggle display");

            // Current Status
            GUILayout.Space(5);
            GUILayout.Label("Current Status:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
            
            if (isCurrentlyTransitioning)
            {
                float duration = Time.time - currentTransitionStartTime;
                GUILayout.Label($"Transitioning to: {currentTransitionTarget} ({duration:F1}s)", GUI.skin.label);
            }
            else
            {
                GUILayout.Label("No active transition", GUI.skin.label);
            }

            GUILayout.Label($"Active Scenes: {string.Join(", ", activeScenes)}", GUI.skin.label);
            GUILayout.Label($"Async Operations: {activeAsyncOperations}", GUI.skin.label);

            // Performance Stats
            if (completedTransitions > 0)
            {
                GUILayout.Space(5);
                GUILayout.Label("Performance Stats:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
                GUILayout.Label($"Completed Transitions: {completedTransitions}", GUI.skin.label);
                GUILayout.Label($"Average Time: {averageTransitionTime:F2}s", GUI.skin.label);
                GUILayout.Label($"Fastest: {shortestTransition:F2}s, Slowest: {longestTransition:F2}s", GUI.skin.label);
            }

            // Last Failure
            if (!string.IsNullOrEmpty(lastFailureReason))
            {
                GUILayout.Space(5);
                GUILayout.Label("Last Failure:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
                GUILayout.Label($"{lastFailureReason} ({Time.time - lastFailureTime:F1}s ago)", GUI.skin.label);
            }

            // Event Counts
            if (eventCounts.Count > 0)
            {
                GUILayout.Space(5);
                GUILayout.Label("Event Counts:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
                foreach (var kvp in eventCounts.OrderByDescending(x => x.Value).Take(5))
                {
                    GUILayout.Label($"{kvp.Key}: {kvp.Value}", GUI.skin.label);
                }
            }

            // Recent Events
            GUILayout.Space(5);
            GUILayout.Label("Recent Events:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
            
            foreach (var eventMsg in recentEvents.TakeLast(8))
            {
                GUILayout.Label(eventMsg, GUI.skin.label);
            }

            // Controls
            GUILayout.Space(10);
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Clear Events"))
            {
                recentEvents.Clear();
                eventCounts.Clear();
            }
            
            if (GUILayout.Button("Print Summary"))
            {
                PrintDetailedSummary();
            }
            
            if (diagnostics != null && GUILayout.Button("Print Diagnostics"))
            {
                Debug.Log(diagnostics.GetDiagnosticSummary());
            }
            
            GUILayout.EndHorizontal();

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private void PrintDetailedSummary()
        {
            Debug.Log("=== Scene Transition Monitor Summary ===\n" +
                     $"Monitoring Enabled: {enableMonitoring}\n" +
                     $"Currently Transitioning: {isCurrentlyTransitioning}\n" +
                     $"Current Target: {currentTransitionTarget}\n" +
                     $"Active Scenes: {string.Join(", ", activeScenes)}\n" +
                     $"Completed Transitions: {completedTransitions}\n" +
                     $"Average Transition Time: {averageTransitionTime:F2}s\n" +
                     $"Last Failure: {lastFailureReason}\n" +
                     $"Event Counts: {string.Join(", ", eventCounts.Select(x => $"{x.Key}:{x.Value}"))}");
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.sceneUnloaded -= OnSceneUnloaded;
            SceneManager.activeSceneChanged -= OnActiveSceneChanged;
            
            LogEvent("Monitor", "Scene Transition Monitor destroyed");
        }
    }
}

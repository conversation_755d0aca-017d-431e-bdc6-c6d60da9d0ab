# Scene Transition Diagnostics

A comprehensive diagnostic system for monitoring and analyzing scene transition logic and async loading operations in the BTR project.

## Overview

This diagnostic system helps identify why scene transitions fail sometimes and work other times by providing detailed monitoring, logging, and analysis of:

- Scene loading operations and progress
- Async operation states and cancellation tokens
- Event flow between different systems
- Performance metrics and failure patterns
- Memory usage during transitions

## Components

### 1. SceneTransitionDiagnostics
**File:** `SceneTransitionDiagnostics.cs`

The core diagnostic component that monitors all scene transition events and maintains detailed logs.

**Features:**
- Tracks all scene loading events from Unity, GameEvents, SceneEvents, and WaveEvents
- Monitors transition timing and memory usage
- Maintains operation logs with configurable history
- Provides comprehensive diagnostic summaries
- Automatically detects and reports transition failures

### 2. SceneTransitionMonitor
**File:** `SceneTransitionMonitor.cs`

Real-time monitoring tool with on-screen display for immediate feedback during development.

**Features:**
- On-screen diagnostic display (toggle with F12)
- Real-time transition status monitoring
- Performance statistics tracking
- Automatic detection of stuck transitions
- Manual controls for clearing data and printing summaries

### 3. AsyncLoadingAnalyzer
**File:** `AsyncLoadingAnalyzer.cs`

Specialized analyzer for async loading operations and cancellation token behavior.

**Features:**
- Monitors Unity AsyncOperation progress and states
- Tracks cancellation token status and timeouts
- Detects stalled operations and suspicious patterns
- Analyzes retry patterns and failure types
- Provides detailed async operation analysis

### 4. SceneTransitionDiagnosticsManager
**File:** `SceneTransitionDiagnosticsManager.cs`

Manager script that coordinates all diagnostic components and provides easy integration.

**Features:**
- Automatic setup of all diagnostic components
- Integration with existing SceneManagerBTR and LoadingScreen
- Public API for external systems to report events
- Centralized configuration and status reporting

## Quick Setup

### Option 1: Automatic Setup (Recommended)
1. Add `SceneTransitionDiagnosticsManager` to any GameObject in your scene
2. The manager will automatically add and configure all diagnostic components
3. Diagnostics will start monitoring immediately

### Option 2: Manual Setup
1. Add individual diagnostic components to GameObjects in your scene:
   - `SceneTransitionDiagnostics`
   - `SceneTransitionMonitor`
   - `AsyncLoadingAnalyzer`
2. Configure each component's settings in the inspector
3. Ensure they can find your SceneManagerBTR and other systems

## Usage

### Monitoring During Development
1. **On-Screen Display**: Press F12 to toggle the real-time monitor display
2. **Console Logs**: All diagnostic events are logged to the Unity console
3. **Manual Controls**: Use the context menu options or on-screen buttons for manual operations

### Analyzing Failures
When a scene transition fails:
1. Check the on-screen monitor for immediate status
2. Look for error messages in the console with `[SceneTransitionDiagnostics]` prefix
3. Use "Print Diagnostic Summary" to get detailed failure analysis
4. Check the async loading analyzer for cancellation token issues

### Integration with Existing Code
The diagnostic system automatically monitors existing systems, but you can also manually report events:

```csharp
// Get the diagnostics manager
var diagnosticsManager = FindObjectOfType<SceneTransitionDiagnosticsManager>();

// Report a scene transition starting
diagnosticsManager.ReportSceneTransitionStarted("MyScene");

// Report a failure
diagnosticsManager.ReportSceneTransitionFailed("MyScene", "Timeout occurred");

// Report async operations
diagnosticsManager.ReportAsyncOperationStarted("MyScene", asyncOperation, cancellationToken);
```

## Configuration

### SceneTransitionDiagnostics Settings
- `enableDiagnostics`: Master enable/disable switch
- `logDetailedEvents`: Controls verbosity of event logging
- `trackMemoryUsage`: Enable memory usage tracking
- `maxLogEntries`: Maximum number of log entries to keep

### SceneTransitionMonitor Settings
- `enableMonitoring`: Enable real-time monitoring
- `showOnScreenDisplay`: Show/hide the on-screen display
- `autoDetectStuckTransitions`: Automatically detect stuck transitions
- `stuckTransitionTimeout`: Timeout threshold for stuck detection

### AsyncLoadingAnalyzer Settings
- `enableAnalysis`: Enable async operation analysis
- `trackCancellationTokens`: Monitor cancellation token states
- `monitorUniTaskOperations`: Special monitoring for UniTask operations
- `suspiciousLoadTime`: Threshold for flagging slow operations

## Troubleshooting Common Issues

### Scene Transitions Get Stuck
- Check the on-screen monitor for current transition status
- Look for "Stalled progress" warnings in the console
- Verify cancellation tokens are not being cancelled unexpectedly
- Check if multiple transitions are running simultaneously

### Frequent Cancellation Token Errors
- Monitor the AsyncLoadingAnalyzer for token status
- Check if tokens are being cancelled by external systems
- Verify token timeout thresholds are appropriate
- Look for patterns in cancellation timing

### Memory Issues During Transitions
- Enable memory tracking in SceneTransitionDiagnostics
- Monitor memory usage before/after transitions
- Look for memory leaks in the diagnostic summaries
- Check if old scenes are being properly unloaded

### Performance Problems
- Use the performance statistics in SceneTransitionMonitor
- Compare average, fastest, and slowest transition times
- Identify problematic scenes with high failure rates
- Monitor async operation patterns for bottlenecks

## Debug Commands

### Context Menu Options
Right-click on diagnostic components in the inspector for:
- Print Analysis Summary
- Print Diagnostic Summary
- Clear Analysis Data
- Simulate Operation Failure (for testing)

### On-Screen Controls
The monitor display includes buttons for:
- Clear Events
- Print Summary
- Print Diagnostics

### Console Commands
Use the diagnostic manager's public methods:
- `GetIntegrationStatus()`: Check component integration
- `IsMonitoringActive()`: Verify monitoring is working
- `PrintDiagnosticSummary()`: Get comprehensive analysis

## Performance Impact

The diagnostic system is designed to have minimal performance impact:
- Event monitoring uses efficient event subscription patterns
- Memory tracking is optional and can be disabled
- Analysis runs at configurable intervals (default 1 second)
- Log entries are automatically cleaned up to prevent memory growth

For production builds, disable diagnostics by setting `enableDiagnostics = false` on the manager.

## Integration Notes

- The system automatically detects and integrates with SceneManagerBTR
- Works with existing GameEvents and SceneEvents systems
- Compatible with LoadingScreen and WaveEventChannel
- Does not modify existing code - purely observational
- Uses only Debug.Log, Debug.LogWarning, and Debug.LogError for output

## Extending the System

To add custom monitoring:
1. Subscribe to additional events in the diagnostic components
2. Use the public API methods to report custom events
3. Add new analysis patterns to the AsyncLoadingAnalyzer
4. Extend the diagnostic data structures for new metrics

The system is designed to be extensible while maintaining backward compatibility with existing BTR systems.

using UnityEngine;
using UnityEngine.SceneManagement;
using System;
using System.Collections.Generic;
using System.Text;
using Cysharp.Threading.Tasks;

namespace BTR
{
    /// <summary>
    /// Diagnostic script to monitor scene transition logic and async loading operations.
    /// Tracks failures, successes, timing, and event flow to help identify transition issues.
    /// </summary>
    public class SceneTransitionDiagnostics : MonoBehaviour
    {
        [Header("Diagnostic Settings")]
        [SerializeField] private bool enableDiagnostics = true;
        [SerializeField] private bool logDetailedEvents = true;
        [SerializeField] private bool trackMemoryUsage = true;
        [SerializeField] private bool trackLoadingTimes = true;
        [SerializeField] private int maxLogEntries = 100;

        [Header("Monitoring Targets")]
        [SerializeField] private SceneManagerBTR sceneManagerBTR;
        [SerializeField] private GameEvents gameEvents;
        [SerializeField] private SceneEvents sceneEvents;
        [SerializeField] private WaveEventChannel waveEventChannel;
        [SerializeField] private LoadingScreen loadingScreen;

        // Diagnostic Data
        private readonly Queue<DiagnosticEntry> diagnosticLog = new();
        private readonly Dictionary<string, SceneTransitionAttempt> activeTransitions = new();
        private readonly Dictionary<string, SceneTransitionStats> sceneStats = new();
        
        // Timing and State Tracking
        private float lastTransitionStartTime;
        private bool isMonitoringTransition;
        private string currentTransitionScene;
        private int totalTransitionAttempts;
        private int successfulTransitions;
        private int failedTransitions;

        public struct DiagnosticEntry
        {
            public float Timestamp;
            public string Category;
            public string Message;
            public LogType LogLevel;
            public string SceneName;
            public float? MemoryUsage;
        }

        public struct SceneTransitionAttempt
        {
            public string SceneName;
            public float StartTime;
            public float? EndTime;
            public bool Success;
            public string ErrorMessage;
            public float MemoryBefore;
            public float MemoryAfter;
            public List<string> EventFlow;
        }

        public struct SceneTransitionStats
        {
            public string SceneName;
            public int TotalAttempts;
            public int SuccessfulAttempts;
            public int FailedAttempts;
            public float AverageLoadTime;
            public float FastestLoadTime;
            public float SlowestLoadTime;
            public List<string> CommonErrors;
        }

        private void Awake()
        {
            if (!enableDiagnostics) return;
            
            // Auto-find components if not assigned
            if (sceneManagerBTR == null) sceneManagerBTR = FindObjectOfType<SceneManagerBTR>();
            if (gameEvents == null) gameEvents = FindObjectOfType<GameEvents>();
            if (sceneEvents == null) sceneEvents = FindObjectOfType<SceneEvents>();
            if (waveEventChannel == null) waveEventChannel = FindObjectOfType<WaveEventChannel>();
            if (loadingScreen == null) loadingScreen = FindObjectOfType<LoadingScreen>();
        }

        private void Start()
        {
            if (!enableDiagnostics) return;
            
            SubscribeToEvents();
            LogDiagnostic("System", "Scene Transition Diagnostics initialized", LogType.Log);
        }

        private void SubscribeToEvents()
        {
            // Unity Scene Manager Events
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;
            SceneManager.activeSceneChanged += OnActiveSceneChanged;

            // Game Events
            if (gameEvents != null)
            {
                gameEvents.OnSceneLoadStarted += OnSceneLoadStarted;
                gameEvents.OnSceneLoadProgress += OnSceneLoadProgress;
                gameEvents.OnSceneLoadCompleted += OnSceneLoadCompleted;
                gameEvents.OnSceneTransition += OnSceneTransition;
                gameEvents.OnSceneTransitionStarted += OnSceneTransitionStarted;
                gameEvents.OnSceneTransitionCompleted += OnSceneTransitionCompleted;
            }

            // Scene Events
            if (sceneEvents != null)
            {
                sceneEvents.OnSceneLoadRequested += OnSceneLoadRequested;
                sceneEvents.OnSceneLoadStarted += OnSceneLoadStartedDetailed;
                sceneEvents.OnSceneLoadCompleted += OnSceneLoadCompletedDetailed;
                sceneEvents.OnSceneUnloadStarted += OnSceneUnloadStarted;
                sceneEvents.OnSceneUnloadCompleted += OnSceneUnloadCompleted;
            }

            // Wave Events
            if (waveEventChannel != null)
            {
                waveEventChannel.OnWaveCustomEvent += OnWaveCustomEvent;
                waveEventChannel.OnSectionCompleted += OnSectionCompleted;
            }
        }

        private void LogDiagnostic(string category, string message, LogType logLevel = LogType.Log, string sceneName = null, float? memoryUsage = null)
        {
            if (!enableDiagnostics) return;

            var entry = new DiagnosticEntry
            {
                Timestamp = Time.time,
                Category = category,
                Message = message,
                LogLevel = logLevel,
                SceneName = sceneName,
                MemoryUsage = memoryUsage
            };

            diagnosticLog.Enqueue(entry);
            
            // Maintain max log size
            while (diagnosticLog.Count > maxLogEntries)
            {
                diagnosticLog.Dequeue();
            }

            // Output to Unity console with appropriate log level
            string logMessage = $"[SceneTransitionDiagnostics] [{category}] {message}";
            if (sceneName != null) logMessage += $" (Scene: {sceneName})";
            if (memoryUsage.HasValue) logMessage += $" (Memory: {memoryUsage.Value:F2}MB)";

            switch (logLevel)
            {
                case LogType.Error:
                    Debug.LogError(logMessage);
                    break;
                case LogType.Warning:
                    Debug.LogWarning(logMessage);
                    break;
                default:
                    if (logDetailedEvents) Debug.Log(logMessage);
                    break;
            }
        }

        private float GetCurrentMemoryUsage()
        {
            if (!trackMemoryUsage) return 0f;
            return UnityEngine.Profiling.Profiler.GetTotalAllocatedMemory(false) / 1024f / 1024f;
        }

        private void StartTransitionTracking(string sceneName, string triggerSource)
        {
            totalTransitionAttempts++;
            isMonitoringTransition = true;
            currentTransitionScene = sceneName;
            lastTransitionStartTime = Time.time;

            var attempt = new SceneTransitionAttempt
            {
                SceneName = sceneName,
                StartTime = Time.time,
                Success = false,
                MemoryBefore = GetCurrentMemoryUsage(),
                EventFlow = new List<string> { $"Started by: {triggerSource}" }
            };

            activeTransitions[sceneName] = attempt;
            LogDiagnostic("Transition", $"Started transition to '{sceneName}' triggered by {triggerSource}", LogType.Log, sceneName, attempt.MemoryBefore);
        }

        private void EndTransitionTracking(string sceneName, bool success, string errorMessage = null)
        {
            if (!activeTransitions.ContainsKey(sceneName)) return;

            var attempt = activeTransitions[sceneName];
            attempt.EndTime = Time.time;
            attempt.Success = success;
            attempt.ErrorMessage = errorMessage;
            attempt.MemoryAfter = GetCurrentMemoryUsage();

            float loadTime = attempt.EndTime.Value - attempt.StartTime;
            float memoryDelta = attempt.MemoryAfter - attempt.MemoryBefore;

            if (success)
            {
                successfulTransitions++;
                LogDiagnostic("Transition", $"Successfully completed transition to '{sceneName}' in {loadTime:F2}s (Memory: +{memoryDelta:F2}MB)", LogType.Log, sceneName);
            }
            else
            {
                failedTransitions++;
                LogDiagnostic("Transition", $"Failed transition to '{sceneName}' after {loadTime:F2}s: {errorMessage}", LogType.Error, sceneName);
            }

            UpdateSceneStats(attempt);
            activeTransitions.Remove(sceneName);
            
            if (sceneName == currentTransitionScene)
            {
                isMonitoringTransition = false;
                currentTransitionScene = null;
            }
        }

        private void UpdateSceneStats(SceneTransitionAttempt attempt)
        {
            if (!sceneStats.ContainsKey(attempt.SceneName))
            {
                sceneStats[attempt.SceneName] = new SceneTransitionStats
                {
                    SceneName = attempt.SceneName,
                    CommonErrors = new List<string>(),
                    FastestLoadTime = float.MaxValue,
                    SlowestLoadTime = 0f
                };
            }

            var stats = sceneStats[attempt.SceneName];
            stats.TotalAttempts++;
            
            if (attempt.Success)
            {
                stats.SuccessfulAttempts++;
                float loadTime = attempt.EndTime.Value - attempt.StartTime;
                stats.FastestLoadTime = Mathf.Min(stats.FastestLoadTime, loadTime);
                stats.SlowestLoadTime = Mathf.Max(stats.SlowestLoadTime, loadTime);
                
                // Update average
                float totalTime = stats.AverageLoadTime * (stats.SuccessfulAttempts - 1) + loadTime;
                stats.AverageLoadTime = totalTime / stats.SuccessfulAttempts;
            }
            else
            {
                stats.FailedAttempts++;
                if (!string.IsNullOrEmpty(attempt.ErrorMessage) && !stats.CommonErrors.Contains(attempt.ErrorMessage))
                {
                    stats.CommonErrors.Add(attempt.ErrorMessage);
                }
            }

            sceneStats[attempt.SceneName] = stats;
        }

        // Event Handlers
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            LogDiagnostic("Unity", $"Scene loaded: {scene.name} (Mode: {mode})", LogType.Log, scene.name);
            
            if (isMonitoringTransition && scene.name == currentTransitionScene)
            {
                EndTransitionTracking(scene.name, true);
            }
        }

        private void OnSceneUnloaded(Scene scene)
        {
            LogDiagnostic("Unity", $"Scene unloaded: {scene.name}", LogType.Log, scene.name);
        }

        private void OnActiveSceneChanged(Scene current, Scene next)
        {
            LogDiagnostic("Unity", $"Active scene changed: {current.name} -> {next.name}", LogType.Log, next.name);
        }

        private void OnSceneLoadStarted(string sceneName)
        {
            LogDiagnostic("GameEvents", $"Scene load started: {sceneName}", LogType.Log, sceneName);
            if (!isMonitoringTransition) StartTransitionTracking(sceneName, "GameEvents.OnSceneLoadStarted");
        }

        private void OnSceneLoadProgress(float progress)
        {
            if (isMonitoringTransition && logDetailedEvents)
            {
                LogDiagnostic("Progress", $"Load progress: {progress:P1}", LogType.Log, currentTransitionScene);
            }
        }

        private void OnSceneLoadCompleted()
        {
            LogDiagnostic("GameEvents", "Scene load completed", LogType.Log, currentTransitionScene);
        }

        private void OnSceneTransition(string sceneName)
        {
            LogDiagnostic("GameEvents", $"Scene transition requested: {sceneName}", LogType.Log, sceneName);
            if (!isMonitoringTransition) StartTransitionTracking(sceneName, "GameEvents.OnSceneTransition");
        }

        private void OnSceneTransitionStarted()
        {
            LogDiagnostic("GameEvents", "Scene transition started", LogType.Log, currentTransitionScene);
        }

        private void OnSceneTransitionCompleted()
        {
            LogDiagnostic("GameEvents", "Scene transition completed", LogType.Log, currentTransitionScene);
        }

        private void OnSceneLoadRequested(string sceneName)
        {
            LogDiagnostic("SceneEvents", $"Scene load requested: {sceneName}", LogType.Log, sceneName);
            if (!isMonitoringTransition) StartTransitionTracking(sceneName, "SceneEvents.OnSceneLoadRequested");
        }

        private void OnSceneLoadStartedDetailed(string sceneName)
        {
            LogDiagnostic("SceneEvents", $"Scene load started (detailed): {sceneName}", LogType.Log, sceneName);
        }

        private void OnSceneLoadCompletedDetailed(string sceneName)
        {
            LogDiagnostic("SceneEvents", $"Scene load completed (detailed): {sceneName}", LogType.Log, sceneName);
        }

        private void OnSceneUnloadStarted(string sceneName)
        {
            LogDiagnostic("SceneEvents", $"Scene unload started: {sceneName}", LogType.Log, sceneName);
        }

        private void OnSceneUnloadCompleted(string sceneName)
        {
            LogDiagnostic("SceneEvents", $"Scene unload completed: {sceneName}", LogType.Log, sceneName);
        }

        private void OnWaveCustomEvent(string eventType, int waveNumber)
        {
            LogDiagnostic("WaveEvents", $"Wave custom event: {eventType} (Wave: {waveNumber})", LogType.Log);
            
            if (eventType.ToLower() == "switch scene")
            {
                LogDiagnostic("WaveEvents", "Scene switch triggered by wave event", LogType.Log);
            }
        }

        private void OnSectionCompleted()
        {
            LogDiagnostic("WaveEvents", "Section completed - potential scene transition trigger", LogType.Log);
        }

        // Public API for external monitoring
        public void LogCustomEvent(string category, string message, LogType logLevel = LogType.Log)
        {
            LogDiagnostic(category, message, logLevel);
        }

        public void ReportTransitionFailure(string sceneName, string errorMessage)
        {
            if (isMonitoringTransition && sceneName == currentTransitionScene)
            {
                EndTransitionTracking(sceneName, false, errorMessage);
            }
            else
            {
                LogDiagnostic("External", $"Transition failure reported for {sceneName}: {errorMessage}", LogType.Error, sceneName);
            }
        }

        public string GetDiagnosticSummary()
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== Scene Transition Diagnostics Summary ===");
            sb.AppendLine($"Total Attempts: {totalTransitionAttempts}");
            sb.AppendLine($"Successful: {successfulTransitions}");
            sb.AppendLine($"Failed: {failedTransitions}");
            sb.AppendLine($"Success Rate: {(totalTransitionAttempts > 0 ? (successfulTransitions / (float)totalTransitionAttempts * 100f) : 0f):F1}%");
            sb.AppendLine($"Currently Monitoring: {(isMonitoringTransition ? currentTransitionScene : "None")}");
            sb.AppendLine();

            if (sceneStats.Count > 0)
            {
                sb.AppendLine("=== Per-Scene Statistics ===");
                foreach (var kvp in sceneStats)
                {
                    var stats = kvp.Value;
                    sb.AppendLine($"Scene: {stats.SceneName}");
                    sb.AppendLine($"  Attempts: {stats.TotalAttempts} (Success: {stats.SuccessfulAttempts}, Failed: {stats.FailedAttempts})");
                    if (stats.SuccessfulAttempts > 0)
                    {
                        sb.AppendLine($"  Load Times: Avg {stats.AverageLoadTime:F2}s, Fastest {stats.FastestLoadTime:F2}s, Slowest {stats.SlowestLoadTime:F2}s");
                    }
                    if (stats.CommonErrors.Count > 0)
                    {
                        sb.AppendLine($"  Common Errors: {string.Join(", ", stats.CommonErrors)}");
                    }
                    sb.AppendLine();
                }
            }

            return sb.ToString();
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.sceneUnloaded -= OnSceneUnloaded;
            SceneManager.activeSceneChanged -= OnActiveSceneChanged;

            if (gameEvents != null)
            {
                gameEvents.OnSceneLoadStarted -= OnSceneLoadStarted;
                gameEvents.OnSceneLoadProgress -= OnSceneLoadProgress;
                gameEvents.OnSceneLoadCompleted -= OnSceneLoadCompleted;
                gameEvents.OnSceneTransition -= OnSceneTransition;
                gameEvents.OnSceneTransitionStarted -= OnSceneTransitionStarted;
                gameEvents.OnSceneTransitionCompleted -= OnSceneTransitionCompleted;
            }

            if (sceneEvents != null)
            {
                sceneEvents.OnSceneLoadRequested -= OnSceneLoadRequested;
                sceneEvents.OnSceneLoadStarted -= OnSceneLoadStartedDetailed;
                sceneEvents.OnSceneLoadCompleted -= OnSceneLoadCompletedDetailed;
                sceneEvents.OnSceneUnloadStarted -= OnSceneUnloadStarted;
                sceneEvents.OnSceneUnloadCompleted -= OnSceneUnloadCompleted;
            }

            if (waveEventChannel != null)
            {
                waveEventChannel.OnWaveCustomEvent -= OnWaveCustomEvent;
                waveEventChannel.OnSectionCompleted -= OnSectionCompleted;
            }

            LogDiagnostic("System", "Scene Transition Diagnostics destroyed", LogType.Log);
        }
    }
}

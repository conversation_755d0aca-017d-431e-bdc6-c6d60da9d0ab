using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using BTR;
using UnityEngine.Events;
using System;

namespace BTR.Management
{
    /// <summary>
    /// Enhanced scene controller for boss levels and non-wave-based scenes.
    /// Supports multiple trigger types and flexible transition conditions.
    /// Includes robust error handling and null safety for reliable scene transitions.
    /// </summary>
    public class BossSceneController : MonoBehaviour
    {
        [Head<PERSON>("Transition Triggers")]
        [Tooltip("Enable multiple transition triggers simultaneously")]
        [SerializeField] private SceneTransitionTriggers enabledTriggers = SceneTransitionTriggers.EnemyDefeat;
        
        [Tooltip("Require ALL enabled triggers to complete, or just ANY one trigger")]
        [SerializeField] private bool requireAllTriggers = false;
        
        [<PERSON><PERSON>("Enemy Defeat Settings")]
        [Tooltip("List of enemies that need to be defeated to complete the scene")]
        [SerializeField] private List<EnemyCore> enemiesInScene = new List<EnemyCore>();
        
        [Tooltip("Auto-find enemies with this tag instead of manual assignment")]
        [SerializeField] private string enemyTag = "Boss";
        
        [Tooltip("Whether to find enemies by tag on start")]
        [SerializeField] private bool findEnemiesByTag = true;
        
        [Header("Timer Settings")]
        [Tooltip("Time in seconds before triggering scene transition")]
        [SerializeField] private float timerDuration = 60f;
        
        [Header("Trigger Settings")]
        [Tooltip("GameObject that must be activated to trigger transition")]
        [SerializeField] private GameObject triggerObject;
        
        [Tooltip("Key that must be pressed to trigger transition")]
        [SerializeField] private KeyCode triggerKey = KeyCode.Space;
        
        [Header("Transition Settings")]
        [Tooltip("Delay in seconds before triggering scene transition")]
        [SerializeField] private float transitionDelay = 3.0f;
        
        [Tooltip("Use custom wave event instead of direct scene transition")]
        [SerializeField] private bool useCustomEvent = false;
        
        [Tooltip("Custom event name to trigger")]
        [SerializeField] private string customEventName = "switch scene";
        
        [Tooltip("Show debug messages in console")]
        [SerializeField] private bool debugLog = true;
        
        [Header("Events")]
        [Tooltip("Event triggered when scene completion conditions are met")]
        [SerializeField] private UnityEvent OnSceneCompleted;
        
        [Tooltip("Event triggered just before scene transition")]
        [SerializeField] private UnityEvent OnTransitionStarted;

        // Private fields for tracking state
        private bool sceneCompleted = false;
        private bool transitionScheduled = false;
        private int enemiesDefeated = 0;
        private bool timerStarted = false;
        private float timerStartTime = 0f;
        
        // Trigger completion tracking
        private bool enemyDefeatCompleted = false;
        private bool timerCompleted = false;
        private bool triggerObjectCompleted = false;
        private bool manualKeyCompleted = false;



        private void Start()
        {
            InitializeSceneController();
        }

        private void Update()
        {
            if (sceneCompleted || transitionScheduled) return;

            // Check all enabled triggers
            if (HasTrigger(SceneTransitionTriggers.Timer))
            {
                HandleTimerUpdate();
            }
            
            if (HasTrigger(SceneTransitionTriggers.TriggerObject))
            {
                HandleTriggerObjectUpdate();
            }
            
            if (HasTrigger(SceneTransitionTriggers.ManualKey))
            {
                CheckManualKeyInput();
            }
        }
        
        /// <summary>
        /// Check if all required triggers are completed and trigger scene transition
        /// </summary>
        private void CheckAllTriggersCompletion()
        {
            if (transitionScheduled || sceneCompleted) return;
            
            try
            {
                bool shouldTransition = false;
                
                if (requireAllTriggers)
                {
                    // ALL enabled triggers must be completed
                    shouldTransition = true;
                    
                    if (HasTrigger(SceneTransitionTriggers.EnemyDefeat) && !enemyDefeatCompleted)
                        shouldTransition = false;
                    if (HasTrigger(SceneTransitionTriggers.Timer) && !timerCompleted)
                        shouldTransition = false;
                    if (HasTrigger(SceneTransitionTriggers.TriggerObject) && !triggerObjectCompleted)
                        shouldTransition = false;
                    if (HasTrigger(SceneTransitionTriggers.ManualKey) && !manualKeyCompleted)
                        shouldTransition = false;
                }
                else
                {
                    // ANY enabled trigger completion is sufficient
                    if (HasTrigger(SceneTransitionTriggers.EnemyDefeat) && enemyDefeatCompleted)
                        shouldTransition = true;
                    if (HasTrigger(SceneTransitionTriggers.Timer) && timerCompleted)
                        shouldTransition = true;
                    if (HasTrigger(SceneTransitionTriggers.TriggerObject) && triggerObjectCompleted)
                        shouldTransition = true;
                    if (HasTrigger(SceneTransitionTriggers.ManualKey) && manualKeyCompleted)
                        shouldTransition = true;
                }
                
                if (shouldTransition)
                {
                    if (debugLog)
                    {
                        string mode = requireAllTriggers ? "ALL" : "ANY";
                        Debug.Log($"[BossSceneController] Trigger condition met ({mode} mode) - initiating scene transition");
                    }
                    
                    TriggerSceneCompletion();
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error checking trigger completion: {e.Message}");
            }
        }

        private void InitializeSceneController()
        {
            // Initialize all enabled trigger types
            if (HasTrigger(SceneTransitionTriggers.EnemyDefeat))
            {
                SetupEnemyTracking();
            }
            
            if (HasTrigger(SceneTransitionTriggers.Timer))
            {
                StartTimer();
            }
            
            if (HasTrigger(SceneTransitionTriggers.TriggerObject))
            {
                SetupTriggerObjectMonitoring();
            }
            
            if (HasTrigger(SceneTransitionTriggers.ManualKey))
            {
                SetupManualKeyMonitoring();
            }
            
            if (HasTrigger(SceneTransitionTriggers.Immediate))
            {
                TriggerSceneCompletion();
            }

            if (debugLog)
            {
                Debug.Log($"[BossSceneController] Initialized with triggers: {enabledTriggers} (Require All: {requireAllTriggers})");
            }
        }
        
        /// <summary>
        /// Check if a specific trigger type is enabled
        /// </summary>
        private bool HasTrigger(SceneTransitionTriggers trigger)
        {
            return (enabledTriggers & trigger) == trigger;
        }

        #region Enemy Defeat Logic
        
        private void SetupEnemyTracking()
        {
            try
            {
                if (debugLog) Debug.Log("[BossSceneController] Setting up enemy tracking...");
                
                // Find enemies by tag if enabled
                if (findEnemiesByTag)
                {
                    if (string.IsNullOrEmpty(enemyTag))
                    {
                        Debug.LogError("[BossSceneController] Enemy tag is null or empty! Cannot find enemies by tag.");
                        return;
                    }
                    
                    GameObject[] taggedEnemies = GameObject.FindGameObjectsWithTag(enemyTag);
                    
                    if (taggedEnemies == null || taggedEnemies.Length == 0)
                    {
                        Debug.LogWarning($"[BossSceneController] No enemies found with tag '{enemyTag}'. Scene may not transition properly.");
                    }
                    else
                    {
                        enemiesInScene.Clear();
                        
                        foreach (var enemy in taggedEnemies)
                        {
                            if (enemy == null)
                            {
                                Debug.LogWarning("[BossSceneController] Found null enemy GameObject in tagged enemies array");
                                continue;
                            }
                            
                            EnemyCore enemyCore = enemy.GetComponent<EnemyCore>();
                            if (enemyCore != null)
                            {
                                enemiesInScene.Add(enemyCore);
                                if (debugLog) Debug.Log($"[BossSceneController] Added enemy to tracking: {enemy.name}");
                            }
                            else
                            {
                                Debug.LogWarning($"[BossSceneController] Enemy '{enemy.name}' with tag '{enemyTag}' does not have EnemyCore component!");
                            }
                        }
                    }
                }
                
                // Validate we have enemies to track
                if (enemiesInScene == null || enemiesInScene.Count == 0)
                {
                    Debug.LogError("[BossSceneController] No valid enemies found to track! Scene transition will not work.");
                    Debug.LogError("[BossSceneController] Please either: 1) Enable 'findEnemiesByTag' and set correct tag, or 2) Manually assign enemies in inspector");
                    return;
                }

                // Setup death listeners
                SetupEnemyListeners();
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error setting up enemy tracking: {e.Message}\nStackTrace: {e.StackTrace}");
            }
            
            // Log the initial count
            if (debugLog)
            {
                Debug.Log($"[BossSceneController] Tracking {enemiesInScene.Count} enemies for scene completion");
                foreach (var enemy in enemiesInScene)
                {
                    Debug.Log($"  - {enemy.gameObject.name}");
                }
            }
        }

        private void SetupEnemyListeners()
        {
            try
            {
                if (enemiesInScene == null)
                {
                    Debug.LogError("[BossSceneController] enemiesInScene list is null! Cannot setup listeners.");
                    return;
                }
                
                // Clean the list first (remove any null entries)
                int originalCount = enemiesInScene.Count;
                enemiesInScene.RemoveAll(enemy => enemy == null);
                
                if (enemiesInScene.Count != originalCount)
                {
                    Debug.LogWarning($"[BossSceneController] Removed {originalCount - enemiesInScene.Count} null enemies from tracking list");
                }
                
                if (enemiesInScene.Count == 0)
                {
                    Debug.LogError("[BossSceneController] No valid enemies remain after cleanup! Scene transition will not work.");
                    return;
                }
                
                // Setup listeners for each enemy's death
                int listenersAdded = 0;
                foreach (var enemy in enemiesInScene)
                {
                    if (enemy != null)
                    {
                        try
                        {
                            enemy.OnDeath += OnEnemyDefeated;
                            listenersAdded++;
                            if (debugLog) Debug.Log($"[BossSceneController] Added death listener to {enemy.gameObject.name}");
                        }
                        catch (Exception e)
                        {
                            Debug.LogError($"[BossSceneController] Failed to add death listener to {enemy.gameObject.name}: {e.Message}");
                        }
                    }
                }
                
                if (debugLog) Debug.Log($"[BossSceneController] Successfully added {listenersAdded}/{enemiesInScene.Count} death listeners");
                
                if (listenersAdded == 0)
                {
                    Debug.LogError("[BossSceneController] No death listeners were added! Scene transition will not work.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error setting up enemy listeners: {e.Message}\nStackTrace: {e.StackTrace}");
            }
        }

        private void OnEnemyDefeated()
        {
            try
            {
                enemiesDefeated++;
                
                if (debugLog) Debug.Log($"[BossSceneController] Enemy defeated: {enemiesDefeated}/{enemiesInScene?.Count ?? 0}");
                
                // Validate the defeat count
                if (enemiesInScene != null && enemiesDefeated > enemiesInScene.Count)
                {
                    Debug.LogWarning($"[BossSceneController] More enemies defeated ({enemiesDefeated}) than tracked ({enemiesInScene.Count})! This may indicate duplicate event subscriptions.");
                }
                
                CheckEnemyDefeatCompletion();
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error in OnEnemyDefeated: {e.Message}\nStackTrace: {e.StackTrace}");
            }
        }

        private void CheckEnemyDefeatCompletion()
        {
            if (enemyDefeatCompleted) return;
            
            if (enemiesInScene != null && enemiesDefeated >= enemiesInScene.Count)
            {
                enemyDefeatCompleted = true;
                if (debugLog) Debug.Log($"[BossSceneController] All {enemiesInScene.Count} enemies defeated!");
                
                CheckAllTriggersCompletion();
            }
        }
        
        #endregion

        #region Trigger Object Logic
        
        private void SetupTriggerObjectMonitoring()
        {
            try
            {
                if (triggerObject == null)
                {
                    Debug.LogWarning("[BossSceneController] Trigger object is null! Cannot monitor trigger object.");
                    return;
                }
                
                if (debugLog) Debug.Log($"[BossSceneController] Monitoring trigger object: {triggerObject.name}");
                
                // Check if already active
                if (triggerObject.activeInHierarchy)
                {
                    OnTriggerObjectActivated();
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error setting up trigger object monitoring: {e.Message}");
            }
        }
        
        private void HandleTriggerObjectUpdate()
        {
            if (triggerObjectCompleted || triggerObject == null) return;
            
            // Check if trigger object became active
            if (triggerObject.activeInHierarchy)
            {
                OnTriggerObjectActivated();
            }
        }
        
        private void OnTriggerObjectActivated()
        {
            if (triggerObjectCompleted) return;
            
            triggerObjectCompleted = true;
            if (debugLog) Debug.Log("[BossSceneController] Trigger object activated!");
            
            CheckAllTriggersCompletion();
        }
        
        #endregion
        
        #region Manual Key Logic
        
        private void SetupManualKeyMonitoring()
        {
            if (debugLog) Debug.Log($"[BossSceneController] Monitoring manual key: {triggerKey}");
        }
        
        private void CheckManualKeyInput()
        {
            if (manualKeyCompleted) return;
            
            if (Input.GetKeyDown(triggerKey))
            {
                OnManualKeyPressed();
            }
        }
        
        private void OnManualKeyPressed()
        {
            if (manualKeyCompleted) return;
            
            manualKeyCompleted = true;
            if (debugLog) Debug.Log($"[BossSceneController] Manual key {triggerKey} pressed!");
            
            CheckAllTriggersCompletion();
        }
        
        #endregion

        #region Timer Logic
        
        private void StartTimer()
        {
            timerStartTime = Time.time;
            timerStarted = true;
            
            if (debugLog) Debug.Log($"[BossSceneController] Timer started: {timerDuration} seconds");
        }

        private void HandleTimerUpdate()
        {
            if (!timerStarted || timerCompleted) return;

            float elapsed = Time.time - timerStartTime;
            if (elapsed >= timerDuration)
            {
                timerCompleted = true;
                if (debugLog) Debug.Log($"[BossSceneController] Timer completed: {elapsed:F1} seconds");
                
                CheckAllTriggersCompletion();
            }
        }
        
        #endregion

        /// <summary>
        /// Call this method from other scripts to manually trigger scene completion
        /// </summary>
        public void TriggerManualCompletion()
        {
            if (debugLog) Debug.Log("[BossSceneController] Manual completion triggered via script");
            TriggerSceneCompletion();
        }

        #region Scene Completion
        
        private void TriggerSceneCompletion()
        {
            if (sceneCompleted || transitionScheduled) return;
            
            sceneCompleted = true;
            transitionScheduled = true;
            
            if (debugLog) Debug.Log($"[BossSceneController] Scene completed! Scheduling transition in {transitionDelay} seconds");
            
            // Trigger completion event
            OnSceneCompleted?.Invoke();
            
            StartCoroutine(TriggerSceneTransitionAfterDelay());
        }

        private IEnumerator TriggerSceneTransitionAfterDelay()
        {
            yield return new WaitForSeconds(transitionDelay);
            
            if (debugLog) Debug.Log("[BossSceneController] Triggering scene transition now");
            
            // Trigger transition started event
            OnTransitionStarted?.Invoke();
            
            TriggerSceneTransition();
        }

        private void TriggerSceneTransition()
        {
            try
            {
                if (debugLog) Debug.Log("[BossSceneController] Attempting scene transition...");
                
                bool transitionSuccessful = false;
                
                // Option 1: Use custom wave event
                if (useCustomEvent)
                {
                    transitionSuccessful = TryCustomEventTransition();
                }
                // Option 2: Direct scene transition
                else
                {
                    transitionSuccessful = TryDirectSceneTransition();
                }
                
                // If primary method failed, try fallback approaches
                if (!transitionSuccessful)
                {
                    if (debugLog) Debug.LogWarning("[BossSceneController] Primary transition method failed, trying fallbacks...");
                    TryFallbackTransition();
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Exception during scene transition: {e.Message}\nStackTrace: {e.StackTrace}");
                
                // Last resort: try direct transition without any checks
                TryEmergencyTransition();
            }
        }
        
        private bool TryCustomEventTransition()
        {
            try
            {
                if (string.IsNullOrEmpty(customEventName))
                {
                    Debug.LogError("[BossSceneController] Custom event name is null or empty!");
                    return false;
                }
                
                var waveEventChannel = FindFirstObjectByType<WaveEventChannel>();
                if (waveEventChannel != null)
                {
                    if (debugLog) Debug.Log($"[BossSceneController] Sending custom event: {customEventName}");
                    waveEventChannel.TriggerWaveCustomEvent(customEventName, 0);
                    return true;
                }
                else
                {
                    Debug.LogError("[BossSceneController] WaveEventChannel not found for custom event");
                    return false;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error in custom event transition: {e.Message}");
                return false;
            }
        }
        
        private bool TryDirectSceneTransition()
        {
            try
            {
                // Multiple attempts to get SceneManagerBTR instance
                SceneManagerBTR sceneManager = null;
                
                // Attempt 1: Direct instance access
                sceneManager = SceneManagerBTR.Instance;
                
                // Attempt 2: FindObjectOfType if instance is null
                if (sceneManager == null)
                {
                    if (debugLog) Debug.LogWarning("[BossSceneController] SceneManagerBTR.Instance is null, searching scene...");
                    sceneManager = FindFirstObjectByType<SceneManagerBTR>();
                }
                
                // Attempt 3: Wait a frame and try again
                if (sceneManager == null)
                {
                    if (debugLog) Debug.LogWarning("[BossSceneController] SceneManagerBTR not found, starting retry coroutine...");
                    StartCoroutine(RetryDirectTransition());
                    return true; // Return true since we're attempting retry
                }
                
                if (sceneManager != null)
                {
                    if (debugLog) Debug.Log("[BossSceneController] Calling SceneManagerBTR.ChangeSceneWithTransitionToNext()");
                    sceneManager.ChangeSceneWithTransitionToNext();
                    return true;
                }
                else
                {
                    Debug.LogError("[BossSceneController] SceneManagerBTR is completely unavailable!");
                    return false;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error in direct scene transition: {e.Message}");
                return false;
            }
        }
        
        private IEnumerator RetryDirectTransition()
        {
            const int maxRetries = 5;
            const float retryDelay = 0.5f;
            
            for (int i = 0; i < maxRetries; i++)
            {
                yield return new WaitForSeconds(retryDelay);
                
                var sceneManager = SceneManagerBTR.Instance;
                if (sceneManager == null)
                {
                    sceneManager = FindFirstObjectByType<SceneManagerBTR>();
                }
                
                if (sceneManager != null)
                {
                    if (debugLog) Debug.Log($"[BossSceneController] Retry {i + 1} successful - calling ChangeSceneWithTransitionToNext()");
                    sceneManager.ChangeSceneWithTransitionToNext();
                    yield break;
                }
                
                if (debugLog) Debug.LogWarning($"[BossSceneController] Retry {i + 1}/{maxRetries} failed - SceneManagerBTR still not available");
            }
            
            Debug.LogError($"[BossSceneController] All {maxRetries} retries failed - cannot find SceneManagerBTR!");
            TryEmergencyTransition();
        }
        
        private void TryFallbackTransition()
        {
            try
            {
                // Fallback 1: Try the opposite method
                if (useCustomEvent)
                {
                    if (debugLog) Debug.Log("[BossSceneController] Fallback: Trying direct transition instead of custom event");
                    if (TryDirectSceneTransition()) return;
                }
                else
                {
                    if (debugLog) Debug.Log("[BossSceneController] Fallback: Trying custom event instead of direct transition");
                    if (TryCustomEventTransition()) return;
                }
                
                // Fallback 2: Try to trigger via GameEvents if available
                var gameEvents = FindFirstObjectByType<GameEvents>();
                if (gameEvents != null)
                {
                    if (debugLog) Debug.Log("[BossSceneController] Fallback: Attempting to trigger via GameEvents");
                    // Note: This would require a method in GameEvents to trigger scene transition
                    // gameEvents.TriggerSceneTransition(); // Uncomment if this method exists
                }
                
                // Fallback 3: Manual scene loading as last resort
                TryEmergencyTransition();
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error in fallback transition: {e.Message}");
                TryEmergencyTransition();
            }
        }
        
        private void TryEmergencyTransition()
        {
            try
            {
                Debug.LogWarning("[BossSceneController] EMERGENCY: Attempting manual scene transition as last resort");
                
                // This is a last-resort emergency transition
                // You might need to implement this based on your specific scene setup
                
                // Option 1: Try to load next scene directly if we know the scene name
                // UnityEngine.SceneManagement.SceneManager.LoadScene("NextSceneName");
                
                // Option 2: Log critical error for manual intervention
                Debug.LogError("[BossSceneController] CRITICAL: All scene transition methods failed! Manual intervention required.");
                Debug.LogError("[BossSceneController] Please check: 1) SceneManagerBTR exists 2) WaveEventChannel exists 3) Scene configuration");
                
                // Option 3: Try to restart current scene as fallback
                // var currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
                // UnityEngine.SceneManagement.SceneManager.LoadScene(currentScene.name);
                
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] CRITICAL ERROR: Even emergency transition failed: {e.Message}");
            }
        }
        
        #endregion

        #region Public Interface
        
        /// <summary>
        /// Add an enemy to track at runtime
        /// </summary>
        public void RegisterEnemy(EnemyCore enemy)
        {
            try
            {
                if (enemy == null)
                {
                    Debug.LogWarning("[BossSceneController] Attempted to register null enemy");
                    return;
                }
                
                if (enemiesInScene == null)
                {
                    enemiesInScene = new List<EnemyCore>();
                }
                
                if (!enemiesInScene.Contains(enemy))
                {
                    enemiesInScene.Add(enemy);
                    enemy.OnDeath += OnEnemyDefeated;
                    
                    if (debugLog) Debug.Log($"[BossSceneController] Registered enemy: {enemy.gameObject.name}");
                }
                else if (debugLog)
                {
                    Debug.Log($"[BossSceneController] Enemy {enemy.gameObject.name} already registered");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error registering enemy: {e.Message}");
            }
        }

        /// <summary>
        /// Force check if all enemies are defeated
        /// </summary>
        public void ForceCheckCompletion()
        {
            if (HasTrigger(SceneTransitionTriggers.EnemyDefeat))
            {
                if (debugLog) Debug.Log("[BossSceneController] Force checking enemy defeat status");
                
                int aliveCount = 0;
                if (enemiesInScene != null)
                {
                    foreach (var enemy in enemiesInScene)
                    {
                        if (enemy != null && enemy.IsAlive)
                        {
                            aliveCount++;
                        }
                    }
                }
                
                if (aliveCount == 0 && !transitionScheduled)
                {
                    if (debugLog) Debug.Log("[BossSceneController] Force check: All enemies are defeated!");
                    enemiesDefeated = enemiesInScene?.Count ?? 0;
                    enemyDefeatCompleted = true;
                    CheckAllTriggersCompletion();
                }
                else if (debugLog)
                {
                    Debug.Log($"[BossSceneController] Force check: {aliveCount} enemies still alive");
                }
            }
        }

        /// <summary>
        /// Get current completion progress (0-1)
        /// </summary>
        public float GetCompletionProgress()
        {
            if (sceneCompleted) return 1f;
            
            float totalProgress = 0f;
            int activeTriggersCount = 0;
            
            // Calculate progress for each active trigger
            if (HasTrigger(SceneTransitionTriggers.EnemyDefeat))
            {
                activeTriggersCount++;
                if (enemiesInScene != null && enemiesInScene.Count > 0)
                {
                    totalProgress += (float)enemiesDefeated / enemiesInScene.Count;
                }
                else
                {
                    totalProgress += enemyDefeatCompleted ? 1f : 0f;
                }
            }
            
            if (HasTrigger(SceneTransitionTriggers.Timer))
            {
                activeTriggersCount++;
                if (timerStarted)
                {
                    float elapsed = Time.time - timerStartTime;
                    totalProgress += Mathf.Clamp01(elapsed / timerDuration);
                }
                else
                {
                    totalProgress += timerCompleted ? 1f : 0f;
                }
            }
            
            if (HasTrigger(SceneTransitionTriggers.TriggerObject))
            {
                activeTriggersCount++;
                totalProgress += triggerObjectCompleted ? 1f : 0f;
            }
            
            if (HasTrigger(SceneTransitionTriggers.ManualKey))
            {
                activeTriggersCount++;
                totalProgress += manualKeyCompleted ? 1f : 0f;
            }
            
            if (HasTrigger(SceneTransitionTriggers.Immediate))
            {
                return 1f; // Immediate triggers are always complete
            }
            
            // Return average progress across all active triggers
            return activeTriggersCount > 0 ? totalProgress / activeTriggersCount : 0f;
        }

        /// <summary>
        /// Check if scene is completed
        /// </summary>
        public bool IsCompleted => sceneCompleted;
        
        #endregion

        #region Cleanup and Memory Management
        
        /// <summary>
        /// Cleanup event subscriptions to prevent memory leaks
        /// </summary>
        private void OnDestroy()
        {
            try
            {
                if (debugLog) Debug.Log("[BossSceneController] Cleaning up event subscriptions...");
                
                // Unsubscribe from all enemy death events
                if (enemiesInScene != null)
                {
                    foreach (var enemy in enemiesInScene)
                    {
                        if (enemy != null)
                        {
                            try
                            {
                                enemy.OnDeath -= OnEnemyDefeated;
                            }
                            catch (Exception e)
                            {
                                Debug.LogWarning($"[BossSceneController] Error unsubscribing from enemy {enemy.gameObject.name}: {e.Message}");
                            }
                        }
                    }
                }
                
                // Stop any running coroutines
                StopAllCoroutines();
                
                if (debugLog) Debug.Log("[BossSceneController] Cleanup completed");
            }
            catch (Exception e)
            {
                Debug.LogError($"[BossSceneController] Error during cleanup: {e.Message}");
            }
        }
        
        /// <summary>
        /// Manually cleanup event subscriptions (useful for scene transitions)
        /// </summary>
        public void CleanupEventSubscriptions()
        {
            OnDestroy();
        }
        
        #endregion

        #region Editor Support
        
        #if UNITY_EDITOR
        /// <summary>
        /// Set enemies list from editor
        /// </summary>
        public void SetEnemies(List<EnemyCore> enemies)
        {
            enemiesInScene = new List<EnemyCore>(enemies);
        }
        #endif
        
        #endregion
    }
    
    /// <summary>
    /// Scene transition trigger types - can be combined using flags
    /// </summary>
    [System.Flags]
    public enum SceneTransitionTriggers
    {
        None = 0,
        EnemyDefeat = 1 << 0,      // 1
        Timer = 1 << 1,           // 2
        TriggerObject = 1 << 2,   // 4
        ManualKey = 1 << 3,       // 8
        Immediate = 1 << 4,       // 16
        
        // Common combinations
        EnemyOrTimer = EnemyDefeat | Timer,
        AllTriggers = EnemyDefeat | Timer | TriggerObject | ManualKey | Immediate
    }
}
